<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>香港華人基督教聯會真道書院</title>
		<link href="{{ asset('static/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
		<link href="{{ asset('static/fontawesome/css/fontawesome.css') }}" rel="stylesheet">
		<link href="{{ asset('static/fontawesome/css/brands.css') }}" rel="stylesheet">
		<link href="{{ asset('static/fontawesome/css/solid.css') }}" rel="stylesheet">
		<link href="{{ asset('static/css/style.css') }}" rel="stylesheet"/>
		<style>
			.signature-pad-container {
				border: 1px solid #ccc;
				border-radius: 4px;
				position: relative;
				width: 100%;
				height: 200px;
				background-color: #fff;
			}
			
			.signature-pad {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
			}
			
			.signature-pad-buttons {
				margin-top: 10px;
			}
			
			.signature-clear-btn {
				background-color: #dc3545;
				border-color: #dc3545;
				color: white;
			}

			#saveSignature {
				background-color: #28a745;
				border-color: #28a745;
				color: white;
			}

			#saveSignature:disabled {
				background-color: #6c757d;
				border-color: #6c757d;
			}
			
			.signature-placeholder {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #999;
				font-style: italic;
				pointer-events: none;
			}
		</style>
	</head>
	<body>
		<div class="container-fluid">
			
			<div class="row">
				<div class="col-xs-12 col-sm-12 col-md-12 col-lg-5 col-xl-4 col-xxl-4 p-0">
					<!-- 左侧侧边栏 -->
					<div class="step-side sticky-lg-top">
						<!-- logo -->
						<div class="header-logo">
							<img src="{{ asset('static/images/B19D5C3408DF47AA72C1B4916D86F1F1.png') }}" alt=""/>
						</div>
						<!-- 进度条 -->
						<div class="step-wrap d-flex flex-column justify-content-start align-items-start">
							<div class="step d-flex flex-row justify-content-start align-items-center">
								<div class="step-left-round d-flex flex-row justify-content-center align-items-center">
									<div></div>
								</div>
								<div class="d-flex flex-column justify-content-start">
									<div class="step-number">Step 1</div>
									<div class="step-title">Job Number & Note Page</div>
								</div>
							</div>
							<div class="step d-flex flex-row justify-content-start align-items-center">
								<div class="step-left-round d-flex flex-row justify-content-center align-items-center">
									<div></div>
								</div>
								<div class="d-flex flex-column justify-content-start">
									<div class="step-number">Step 2</div>
									<div class="step-title">Information</div>
								</div>
							</div>
							<div class="step current-step d-flex flex-row justify-content-start align-items-center">
								<div class="step-left-round d-flex flex-row justify-content-center align-items-center">
									<div></div>
								</div>
								<div class="d-flex flex-column justify-content-start">
									<div class="step-number">Step 3</div>
									<div class="step-title">Preview and Submit</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-12 col-sm-12 col-md-12 col-lg-7 col-xl-8 col-xxl-8 p-4">
					<!-- 右侧内容区域 -->
					<div class="right-content">
						<form action="{{ route('job.step4.submit') }}" method="POST" id="signatureForm">
							@csrf
							<div class="right-card-wrap">
								<div class="content-title d-flex align-items-center">
									<p>Declaration</p>
								</div>
								<div class="right-card-content">
									<p>I hereby declare that:</p>
									<p>* If I provide false information or withhold material information, I am subject to the dire consequences of criminal prosecution, and I may be dismissed by the school;</p>
									<p>* I consent to the HKCCCU Logos Academy making any necessary enquiries for purposes relating to recruitment by and employment with the HKCCCU Logos Academy and for the verification of the information given above. I authorise all government departments and other organisations or agencies to release any record or information as may be required for these enquiries (including, inter alia, obtaining a reference/performance appraisal report(s) from my current and/or previous employer(s) before offer of appointment; obtaining my medical examination reports, medical board reports or medical records from relevant authorities/agencies/medical personnel and transferring of such data to other authorities/agencies/medical personnel; and making enquiries from relevant government departments/institutions/agencies regarding my academic/language/professional qualifications and obtaining relevant records and transferring of such data to other government departments/authorities/agencies for qualifications assessment);</p>
									<p>* I understand and accept that the information given above will be provided to organisations or agencies authorised to process the information for purposes relating to recruitment by and employment with the Government e.g. qualifications assessment, medical examination, employer reference and integrity checking, etc. as may be necessary.</p>
									<h5>eSignature</h5>
									<p>Please read the above Declaration carefully, then acknowledge that you have read and you agree all these terms by providing the information requested at the bottom of the page. Please note that an eSignature is the electronic equivalent of a hand-written signature.</p>
									<h5>Do Not E-Sign Until You Have Read The Above Declaration.</h5>
									<p>By my eSignature below, I certify that I have read, fully understand and accept all terms of the foregoing Declaration.</p>
									
									<div class="row">
										<div class="col-12">
											<div class="signature-pad-container">
												<div class="signature-placeholder">请在此处签名</div>
												<canvas class="signature-pad"></canvas>
											</div>
											<div class="signature-pad-buttons d-flex justify-content-between">
												<button type="button" class="btn btn-sm btn-success" id="saveSignature">保存签名</button>
												<button type="button" class="btn btn-sm signature-clear-btn" id="clearSignature">清除签名</button>
											</div>
											<div id="signatureStatus" class="mt-2"></div>
											<!-- 签名将通过AJAX上传，不需要隐藏字段 -->
										</div>
									</div>
									
									<div class="row mt-3">
										<div class="col-8">
											<input type="text" name="signature_name" placeholder="请输入您的正式姓名" class="form-control custom-input name-input" required />
										</div>
										<div class="col">
											<input type="date" name="signature_date" class="form-control custom-input name-input" value="{{ date('Y-m-d') }}" required />
										</div>
									</div>
									
									<div class="row mt-3">
										<div class="col-12">
											<div class="form-check">
												<input class="form-check-input" type="checkbox" name="declaration" id="declaration" value="1" required>
												<label class="form-check-label" for="declaration">
													I have read and agree to the declaration above.
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- 提交按钮 -->
							<div class="d-flex justify-content-center align-items-center mt-4">
								<button type="submit" class="btn next-btn custom-btn-color1" id="submitBtn">
									SUBMIT
									<i class="fas fa-angle-right" style="float: right; margin-right: 1.25rem;"></i>
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>

		<script src="{{ asset('static/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
		<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.6/dist/signature_pad.umd.min.js"></script>
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				// 初始化签名板
				const canvas = document.querySelector('.signature-pad');
				const signaturePad = new SignaturePad(canvas, {
					backgroundColor: 'rgb(255, 255, 255)',
					penColor: 'rgb(0, 0, 0)'
				});
				
				// 调整画布大小以适应容器
				function resizeCanvas() {
					const container = document.querySelector('.signature-pad-container');
					const ratio = Math.max(window.devicePixelRatio || 1, 1);
					canvas.width = container.clientWidth * ratio;
					canvas.height = container.clientHeight * ratio;
					canvas.getContext("2d").scale(ratio, ratio);
					signaturePad.clear(); // 清除内容
				}
				
				// 初始化和窗口大小变化时调整画布
				window.addEventListener('resize', resizeCanvas);
				resizeCanvas();
				
				// 清除按钮功能
				document.getElementById('clearSignature').addEventListener('click', function() {
					signaturePad.clear();
					document.querySelector('.signature-placeholder').style.display = 'block';
				});
				
				// 当有签名时隐藏占位符
				signaturePad.addEventListener('beginStroke', function() {
					document.querySelector('.signature-placeholder').style.display = 'none';
				});
				
				let signatureUploaded = false;

				// 保存签名按钮点击事件
				document.getElementById('saveSignature').addEventListener('click', function() {
					if (signaturePad.isEmpty()) {
						showMessage('请先完成签名', 'warning');
						return;
					}

					if (signatureUploaded) {
						showMessage('签名已保存', 'info');
						return;
					}

					// 禁用按钮，显示加载状态
					const saveBtn = this;
					saveBtn.disabled = true;
					saveBtn.textContent = '保存中...';

					uploadSignature().finally(() => {
						saveBtn.disabled = false;
						saveBtn.textContent = '保存签名';
					});
				});

				// 上传签名函数
				function uploadSignature() {
					return new Promise((resolve, reject) => {
						signaturePad.canvas.toBlob(function(blob) {
							const formData = new FormData();
							formData.append('signature_file', blob, 'signature.png');
							formData.append('_token', document.querySelector('input[name="_token"]').value);

							fetch('{{ route("job.upload.signature") }}', {
								method: 'POST',
								body: formData
							})
							.then(response => response.json())
							.then(data => {
								if (data.success) {
									signatureUploaded = true;
									console.log('签名上传成功:', data.filepath);
									showMessage('签名已保存', 'success');
									updateSignatureStatus('已保存', 'success');
									resolve(data);
								} else {
									console.error('签名上传失败:', data.message);
									showMessage('签名保存失败: ' + data.message, 'error');
									updateSignatureStatus('保存失败', 'error');
									reject(new Error(data.message));
								}
							})
							.catch(error => {
								console.error('签名上传错误:', error);
								showMessage('签名保存出错，请重试', 'error');
								updateSignatureStatus('保存出错', 'error');
								reject(error);
							});
						}, 'image/png', 0.8);
					});
				}

				// 显示消息函数
				function showMessage(message, type) {
					const alertClass = type === 'success' ? 'alert-success' :
									  type === 'warning' ? 'alert-warning' :
									  type === 'info' ? 'alert-info' : 'alert-danger';
					const messageDiv = document.createElement('div');
					messageDiv.className = `alert ${alertClass} alert-dismissible fade show`;
					messageDiv.innerHTML = `
						${message}
						<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
					`;

					// 插入到表单顶部
					const form = document.getElementById('signatureForm');
					form.insertBefore(messageDiv, form.firstChild);

					// 3秒后自动消失
					setTimeout(() => {
						if (messageDiv.parentNode) {
							messageDiv.remove();
						}
					}, 3000);
				}

				// 更新签名状态显示
				function updateSignatureStatus(status, type) {
					const statusDiv = document.getElementById('signatureStatus');
					const statusClass = type === 'success' ? 'text-success' :
									   type === 'error' ? 'text-danger' : 'text-muted';
					statusDiv.innerHTML = `<small class="${statusClass}">签名状态: ${status}</small>`;
				}

				// 清除签名时重置上传状态
				document.getElementById('clearSignature').addEventListener('click', function() {
					signatureUploaded = false;
					updateSignatureStatus('未保存', 'muted');
					document.querySelector('.signature-placeholder').style.display = 'block';
				});

				// 提交表单前检查
				document.getElementById('signatureForm').addEventListener('submit', function(e) {
					if (signaturePad.isEmpty()) {
						e.preventDefault();
						showMessage('请先完成签名', 'warning');
						return false;
					}

					if (!signatureUploaded) {
						e.preventDefault();
						showMessage('请先点击"保存签名"按钮保存您的签名', 'warning');
						return false;
					}

					// 如果签名已上传，允许正常提交表单
					const submitBtn = this.querySelector('button[type="submit"]');
					submitBtn.disabled = true;
					submitBtn.textContent = '正在提交...';
				});
			});
		</script>
	</body>
</html>