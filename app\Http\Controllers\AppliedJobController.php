<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\AppliedJob;
use App\Models\Category;
use App\Models\Custom;
use App\Models\CustomQuestion;
use App\Models\JobCategory;
use App\Models\JobLocation;
use App\Models\JobSkill;
use App\Models\JobStage;
use App\Models\JobType;
use App\Models\Location;
use App\Models\Notification;
use App\Models\Question;
use App\Models\Skill;
use App\Models\Stage;
 use App\Models\Type;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class AppliedJobController extends Controller
{

    public function index()
    {
        if (\Auth::user()->can('manage applied job')) {
            $appliedJobs = AppliedJob::where('parent_id', parentId())->get();
            return view('applied_job.index', compact('appliedJobs'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        // 分类只允许两个固定值
        $categories = [
            1 => 'teacher',
            2 => 'non-teacher',
        ];
        $categories = collect($categories);
        $categories->prepend(__('Select Category'), '');

        $types = Type::where('parent_id', parentId())->get()->pluck('type', 'id');
        $types->prepend(__('Select Type'), '');

        $locations = Location::where('parent_id', parentId())->get()->pluck('location', 'id');
        $locations->prepend(__('Select Location'), '');

        $skills = Skill::where('parent_id', parentId())->get()->pluck('skill', 'id');

        $status = AppliedJob::$status;
        $salary_period = AppliedJob::$salary_period;

        $questions = Question::where('parent_id', parentId())->get();
        
        // 获取用户和用户组数据
        $users = User::where('parent_id', parentId())->where('is_active', 1)->get();
        $userGroups = \App\Models\UserGroup::all();

        return view('applied_job.create', compact('categories', 'types', 'status', 'questions', 'locations', 'skills', 'salary_period', 'users', 'userGroups'));
    }


    public function store(Request $request)
    {
        if (\Auth::user()->can('create applied job')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'job_title' => 'required',
                    'job_description' => 'required',
                    'job_requirement' => 'required',
                    'job_location' => 'required',
                    'job_category' => 'required',
                    'job_type' => 'required',
                    'job_skill' => 'required',
                    'position' => 'required',
                    'start_date' => 'required',
                    'end_date' => 'required',
                    'min_experience' => 'required',
                    'max_experience' => 'required',
                    'salary_period' => 'required',
                    'min_salary' => 'required',
                    'max_salary' => 'required',
                    'status' => 'required',
                    'code' => 'required|unique:applied_jobs,code',
                ]
            );

            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $job = new AppliedJob();
            $job->job_title = $request->job_title;
            $job->job_description = $request->job_description;
            $job->job_requirement = $request->job_requirement;
            $job->job_location = $request->job_location;
            $job->job_category = $request->job_category;
            $job->job_skill = implode(',', $request->job_skill);
            $job->position = $request->position;
            $job->start_date = $request->start_date;
            $job->end_date = $request->end_date;
            $job->min_experience = $request->min_experience;
            $job->max_experience = $request->max_experience;
            $job->min_salary = $request->min_salary;
            $job->max_salary = $request->max_salary;
            $job->job_type = $request->job_type;
            $job->salary_period = $request->salary_period;
            $job->status = $request->status;
            $job->code = $request->code;
            $job->question = !empty($request->question) ? implode(',', $request->question) : '';
            $job->parent_id = parentId();
            $job->save();
            
            // 处理用户关联
            $userIds = $request->input('assigned_users', []);
            $userGroupIds = $request->input('assigned_user_groups', []);
            $job->assignToUsers($userIds, $userGroupIds);

            return redirect()->route('applied-job.index')->with('success', __('Applied job successfully created.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function show($eId)
    {
        if (\Auth::user()->can('show applied job')) {
            $id = \Crypt::decrypt($eId);
            $appliedJob = AppliedJob::find($id);
            return view('applied_job.show', compact('appliedJob'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function edit($eId)
    {
        $id = \Crypt::decrypt($eId);
        $appliedJob = AppliedJob::find($id);
        $appliedJob->job_skill = explode(',', $appliedJob->job_skill);

        // 分类只允许两个固定值
        $categories = [
            1 => '教师类',
            2 => '非教师类',
        ];
        $categories = collect($categories);
        $categories->prepend(__('Select Category'), '');

        $types = Type::where('parent_id', parentId())->get()->pluck('type', 'id');
        $types->prepend(__('Select Type'), '');

        $locations = Location::where('parent_id', parentId())->get()->pluck('location', 'id');
        $locations->prepend(__('Select Location'), '');

        $skills = Skill::where('parent_id', parentId())->get()->pluck('skill', 'id');

        $status = AppliedJob::$status;
        $salary_period = AppliedJob::$salary_period;
        $questions = Question::where('parent_id', parentId())->get();
        
        // 获取用户和用户组数据
        $users = User::where('parent_id', parentId())->where('is_active', 1)->get();
        $userGroups = \App\Models\UserGroup::all();
        
        // 获取已分配的用户和用户组
        $assignedUserIds = $appliedJob->users()
                                    ->where('source_type', 'direct')
                                    ->pluck('users.id')
                                    ->toArray();
        
        $assignedGroupIds = $appliedJob->users()
                                     ->where('source_type', 'group')
                                     ->distinct()
                                     ->pluck('source_id')
                                     ->toArray();

        return view('applied_job.edit', compact('categories', 'status', 'types', 'salary_period', 'questions', 'locations', 'skills', 'appliedJob', 'users', 'userGroups', 'assignedUserIds', 'assignedGroupIds'));
    }


    public function update(Request $request, AppliedJob $appliedJob)
    {
        if (\Auth::user()->can('edit applied job')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'job_title' => 'required',
                    'job_description' => 'required',
                    'job_requirement' => 'required',
                    'job_location' => 'required',
                    'job_category' => 'required',
                    'job_type' => 'required',
                    'job_skill' => 'required',
                    'position' => 'required',
                    'start_date' => 'required',
                    'end_date' => 'required',
                    'min_experience' => 'required',
                    'max_experience' => 'required',
                    'salary_period' => 'required',
                    'min_salary' => 'required',
                    'max_salary' => 'required',
                    'status' => 'required',
                    'code' => 'required|unique:applied_jobs,code,'.$appliedJob->id,
                ]
            );

            if ($validator->fails()) {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $appliedJob->job_title = $request->job_title;
            $appliedJob->job_description = $request->job_description;
            $appliedJob->job_requirement = $request->job_requirement;
            $appliedJob->job_location = $request->job_location;
            $appliedJob->job_category = $request->job_category;
            $appliedJob->job_skill = implode(',', $request->job_skill);
            $appliedJob->position = $request->position;
            $appliedJob->start_date = $request->start_date;
            $appliedJob->end_date = $request->end_date;
            $appliedJob->min_experience = $request->min_experience;
            $appliedJob->max_experience = $request->max_experience;
            $appliedJob->min_salary = $request->min_salary;
            $appliedJob->max_salary = $request->max_salary;
            $appliedJob->job_type = $request->job_type;
            $appliedJob->salary_period = $request->salary_period;
            $appliedJob->status = $request->status;
            $appliedJob->code = $request->code;
            $appliedJob->question = !empty($request->question) ? implode(',', $request->question) : '';
            $appliedJob->save();
            
            // 处理用户关联
            $userIds = $request->input('assigned_users', []);
            $userGroupIds = $request->input('assigned_user_groups', []);
            $appliedJob->assignToUsers($userIds, $userGroupIds);

            return redirect()->route('applied-job.index')->with('success', __('Applied job successfully updated.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function destroy(AppliedJob $appliedJob)
    {
        $appliedJob->delete();
        return redirect()->back()->with('success', __('Applied job successfully deleted.'));
    }


    public function jobList()
    {
        $appliedJobList = AppliedJob::where('status', 'active')->paginate(10);
        $settings = settingsById(1);

        $categories = Category::get();
        $stages = Stage::get();
        return view('job_page.job_list', compact('appliedJobList','settings','categories','stages'));
    }

    public function jobPage()
    {
        $user = User::where('type', 'owner')->first();
        $settings = settingsById($user->id);

        $appliedJob = AppliedJob::where('parent_id', $user->id)->where('status', 'active')->paginate(10);
        $parent_id = $user->id;
        $categories = Category::where('parent_id', $user->id)->get();
        $stages = Stage::where('parent_id', $user->id)->get();

        // 初始化职位详情变量
        $jobDetails = null;

        return view('job_page.index', compact('settings', 'appliedJob', 'parent_id', 'categories', 'stages', 'user', 'jobDetails'));
    }

    /**
     * 搜索职位编号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchJob(Request $request)
    {
        try {
            // 验证请求数据
            $validator = \Validator::make($request->all(), [
                'job_reference_number' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 400);
            }

            // 允许查询任意状态（active / inactive），仅按编号匹配即可
            $job = AppliedJob::where('code', $request->job_reference_number)->first();

            if (!$job) {
                return response()->json([
                    'success' => false,
                    'message' => '职位编号不存在'
                ], 404);
            }

            // 返回职位信息
            return response()->json([
                'success' => true,
                'job' => [
                    'id' => $job->id,
                    'code' => $job->code,
                    'job_title' => $job->job_title,
                    'job_description' => $job->job_description,
                    'job_requirement' => $job->job_requirement,
                    'position' => $job->position,
                    'min_salary' => $job->min_salary,
                    'max_salary' => $job->max_salary,
                    'salary_period' => $job->salary_period,
                    'job_location' => $job->job_location,
                    'job_category' => $job->job_category,
                    'job_type' => $job->job_type,
                    'start_date' => $job->start_date,
                    'end_date' => $job->end_date,
                    'status' => $job->status, // 添加状态字段
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '搜索失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 处理第一步表单提交，跳转到第二步
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processStep1(Request $request)
    {
        try {
            // 验证请求数据
            $validator = \Validator::make($request->all(), [
                'job_reference_number' => 'required|string|max:255',
                'agree_terms' => 'required|accepted',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // 只根据编号检查职位存在，后续再判断状态
            $job = AppliedJob::where('code', $request->job_reference_number)->first();

            // 无此编号
            if (!$job) {
                return redirect()->back()
                    ->withErrors(['job_reference_number' => '职位编号不存在'])
                    ->withInput();
            }

            // 岗位存在但未开放（inactive）
            if ($job->status !== 'active') {
                return redirect()->back()
                    ->withErrors(['job_reference_number' => '岗位未开放'])
                    ->withInput();
            }

            // 将职位信息存储到session中，供后续步骤使用
            session([
                'current_job_id' => $job->id,
                'current_job_code' => $job->code,
                'current_job_title' => $job->job_title,
                'step1_completed' => true
            ]);

            // 跳转到第二步
            return redirect()->route('job.step2');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => '处理失败，请稍后重试'])
                ->withInput();
        }
    }

    public function jobStep2()
    {
        if (!session('step1_completed')) {
            return redirect()->route('job.page')->withErrors(['请先完成第一步']);
        }
        $jobId = session('current_job_id');
        $job = AppliedJob::find($jobId);
        $questions = [];
        if ($job && $job->question) {
            $questionIds = explode(',', $job->question);
            $questions = \App\Models\Question::whereIn('id', $questionIds)->get();
        }
   
        return view('job_page.information', compact('questions'));
    }

    public function jobStep2Submit(Request $request)
    {
        // 校验必须先完成step1
        if (!session('step1_completed')) {
            return redirect()->route('job.page')->withErrors(['请先完成第一步']);
        }
        // 定义基础必填字段的校验规则
        $rules = [
            // 个人信息
            'surname' => 'required',
            'given_names' => 'required',
            'gender' => 'required',
            'name_in_chinese' => 'required',
            'date_of_birth' => 'required',
            'place_of_birth' => 'required',
            'hkid_no' => 'required',
            'passport_number' => 'required',
            'place_of_issue' => 'required',
            'nationality' => 'required',
            'religion' => 'required',
            'residential_address' => 'required',
            'phone' => 'required',
            'email' => 'required|email',
            'registered_teacher_no' => 'required',
            'availability' => 'required',
            'current_salary' => 'required',
            'salary_expected' => 'required',
            'resume_file' => 'nullable|string', // 添加简历文件路径验证
            // 问题不再用 question.* 的 required
        ];
        $messages = [
            'required' => ':attribute 为必填项',
            'email' => '邮箱格式不正确',
        ];
        $attributes = [
            'surname' => '姓氏',
            'given_names' => '名字',
            'gender' => '性别',
            'name_in_chinese' => '中文名',
            'date_of_birth' => '出生日期',
            'place_of_birth' => '出生地',
            'hkid_no' => '香港身份证号',
            'passport_number' => '护照号',
            'place_of_issue' => '签发地',
            'nationality' => '国籍',
            'religion' => '宗教',
            'residential_address' => '住址',
            'phone' => '电话',
            'email' => '邮箱',
            'registered_teacher_no' => '教师注册编号',
            'availability' => '可到岗时间',
            'current_salary' => '当前薪资',
            'salary_expected' => '期望薪资',
        ];
        $validator = \Validator::make($request->all(), $rules, $messages, $attributes);
        // 自定义校验：学历、专业资格、特殊教育、工作经历、推荐人、问题
        $validator->after(function ($validator) use ($request) {
            // 学历
            $rows = $request->input('academic_attainment', []);
            $valid = false;
            foreach ($rows as $row) {
                if (!empty($row['issuing_authority']) && !empty($row['qualification']) && !empty($row['issue_date']) && !empty($row['major_subjects'])) {
                    $valid = true;
                    break;
                }
            }
            if (!$valid) {
                $validator->errors()->add('academic_attainment', '学历信息至少完整填写一行');
            }
            // 专业资格
            $rows = $request->input('professional_qualification', []);
            $valid = false;
            foreach ($rows as $row) {
                if (!empty($row['issuing_authority']) && !empty($row['qualification']) && isset($row['status']) && $row['status'] !== '' && !empty($row['level']) && !empty($row['issue_date'])) {
                    $valid = true;
                    break;
                }
            }
            if (!$valid) {
                $validator->errors()->add('professional_qualification', '专业资格至少完整填写一行');
            }
            // 特殊教育
            $rows = $request->input('special_education', []);
            $valid = false;
            foreach ($rows as $row) {
                if (!empty($row['issuing_authority']) && !empty($row['qualification']) && isset($row['status']) && $row['status'] !== '' && !empty($row['level']) && !empty($row['issue_date'])) {
                    $valid = true;
                    break;
                }
            }
            if (!$valid) {
                $validator->errors()->add('special_education', '特殊教育培训至少完整填写一行');
            }
            // 工作经历
            $rows = $request->input('work_experience', []);
            $valid = false;
            foreach ($rows as $row) {
                if (!empty($row['employer']) && !empty($row['position']) && !empty($row['date_from']) && !empty($row['date_to'])) {
                    $valid = true;
                    break;
                }
            }
            if (!$valid) {
                $validator->errors()->add('work_experience', '工作经历至少完整填写一行');
            }
            // 推荐人
            $rows = $request->input('referee', []);
            $valid = false;
            foreach ($rows as $row) {
                if (!empty($row['name']) && !empty($row['company']) && !empty($row['position']) && !empty($row['contact_number'])) {
                    $valid = true;
                    break;
                }
            }
            if (!$valid) {
                $validator->errors()->add('referee', '推荐人信息至少完整填写一行');
            }
            // 问题：所有问题都必填
            $questions = $request->input('question', []);
            $allFilled = true;
            foreach ($questions as $answer) {
                if (empty($answer)) {
                    $allFilled = false;
                    break;
                }
            }
            if (!$allFilled) {
                $validator->errors()->add('question', '请填写所有问题');
            }
        });
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        // 保存数据到session
        $formData = $request->all();
        
        // 如果session中有简历文件路径，添加到表单数据中
        if (session('resume_file')) {
            $formData['resume_file'] = session('resume_file');
        }
        
        session(['step2_data' => $formData, 'step2_completed' => true]);
        return redirect()->route('job.step3')->with('success', '第二步信息已提交');
    }

    public function appliedJob($id)
    {
        $appliedJob = AppliedJob::where('id', $id)->first();
        $user = User::where('type','owner')->first();
        $settings = settingsById($user->id);

        $categories = Category::where('parent_id', $user->id)->get();
        $stages = Stage::where('parent_id', $user->id)->get();
         if ($appliedJob->status == 'in_active') {
            return redirect()->back()->with('error', __('Permission Denied!'));
        } else {
            return view('job_page.job_details', compact('settings', 'appliedJob', 'categories', 'stages', 'user'));
        }
    }

    public function jobApply( $id)
    {

        $appliedJob = AppliedJob::where('id', $id)->first();
        $user = User::where('type', 'owner')->first();
        $settings = settingsById($user->id);

        $questions = Question::where('parent_id', $user->id)->get();
        $categories = Category::where('parent_id', $user->id)->get();
        $stages = Stage::where('parent_id', $user->id)->get();
        return view('job_page.job_apply', compact('settings', 'appliedJob', 'questions', 'categories', 'stages', 'user'));
    }

    public function applicantData(Request $request, $jobId)
    {
        $user = User::where('type', 'owner')->first();
        $validator = \Validator::make(
            $request->all(),
            [
                'full_name' => 'required',
                'email_address' => 'required',
                'phone_number' => 'required',
                'experience' => 'required',
                'expected_salary' => 'required',
                'gender' => 'required',
                'dob' => 'required',
                'address' => 'required',
                'profile' => 'required',
                'resume' => 'required',
            ]
        );

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first())->withInput();
        }

        $appliedJob = AppliedJob::where('id', $jobId)->first();
        $applicant = new Applicant();
        $applicant->job = $appliedJob->id;
        $applicant->name = $request->full_name;
        $applicant->phone = $request->phone_number;
        $applicant->cover_letter = $request->cover_letter;
        $applicant->experience = $request->experience;
        $applicant->email = $request->email_address;
        $applicant->expected_salary = $request->expected_salary;
        $applicant->dob = $request->dob;
        $applicant->gender = $request->gender;
        $applicant->address = $request->address;
        $applicant->question = json_encode($request->question);
        $applicant->parent_id = $appliedJob->parent_id;

        if (!empty($request->resume)) {
            $resumeFilenameWithExt = $request->file('resume')->getClientOriginalName();
            $resumeFilename = pathinfo($resumeFilenameWithExt, PATHINFO_FILENAME);
            $resumeExtension = $request->file('resume')->getClientOriginalExtension();
            $resumeFileName = $resumeFilename . '_' . time() . '.' . $resumeExtension;
            $directory = storage_path('upload/applicant/resume');
            $filePath = $directory . $resumeFilenameWithExt;
            if (\File::exists($filePath)) {
                \File::delete($filePath);
            }
            if (!file_exists($directory)) {
                mkdir($directory, 0777, true);
            }
            $request->file('resume')->storeAs('upload/applicant/resume/', $resumeFileName);
            $applicant->resume = !empty($request->resume) ? $resumeFileName : null;
        }

        if (!empty($request->profile)) {
            $profileFilenameWithExt = $request->file('profile')->getClientOriginalName();
            $profileFilename = pathinfo($profileFilenameWithExt, PATHINFO_FILENAME);
            $profileExtension = $request->file('profile')->getClientOriginalExtension();
            $profileFileName = $profileFilename . '_' . time() . '.' . $profileExtension;

            $directory = storage_path('upload/applicant/profile');
            $filePath = $directory . $profileFilenameWithExt;

            if (\File::exists($filePath)) {
                \File::delete($filePath);
            }
            if (!file_exists($directory)) {
                mkdir($directory, 0777, true);
            }
            $request->file('profile')->storeAs('upload/applicant/profile/', $profileFileName);
            $applicant->profile = !empty($request->profile) ? $profileFileName : null;
        }
        $applicant->save();


        $module = 'applicant_welcome';
        $notification = Notification::where('parent_id', $user->id)->where('module', $module)->first();
        $setting = settings();
        $errorMessage = '';

        if (!empty($notification) && $notification->enabled_email == 1) {
            $notificationResponse = MessageReplace($notification, $applicant->id);

            $data['subject'] = $notificationResponse['subject'];
            $data['message'] = $notificationResponse['message'];
            $data['module'] = $module;
            $data['logo'] = $setting['company_logo'];
            $data['parent_id'] = $user->id;
            $to = $request->email_address;

            $response = commonEmailSend($to, $data);

            if ($response['status'] == 'error') {
                $errorMessage = $response['message'];
            }
        }

        $owner = User::find($applicant->parent_id);
        $module = 'applicant_create';
        $notification = Notification::where('parent_id', $user->id)->where('module', $module)->first();
        $notification->ownerName = $owner->name;
        $setting = settings();
        $errorMessage = '';

        if (!empty($notification) && $notification->enabled_email == 1) {
            $notificationResponse = MessageReplace($notification, $applicant->id);

            $data['subject'] = $notificationResponse['subject'];
            $data['message'] = $notificationResponse['message'];
            $data['module'] = $module;
            $data['logo'] = $setting['company_logo'];
            $data['parent_id'] = $user->id;
            $to = $owner->email;

            $response = commonEmailSend($to, $data);

            if ($response['status'] == 'error') {
                $errorMessage = $response['message'];
            }
        }

        return redirect()->back()->with('success', __('Your data successfully submited.') . '</br>' . $errorMessage);
    }

    public function jobStep3()
    {
        // 获取step1和step2数据
        $step1 = session('current_job_id') ? [
            'job_id' => session('current_job_id'),
            'job_code' => session('current_job_code'),
            'job_title' => session('current_job_title'),
        ] : [];
        $step2 = session('step2_data', []);
        return view('job_page.preview', compact('step1', 'step2'));
    }

     /**
     * step3表单提交，设置step3_completed
     */
    public function jobStep3Submit(Request $request)
    {
        // 可做校验或数据处理
        session(['step3_completed' => true]);
        return redirect()->route('job.step4');
    }

    /**
     * 第四步：声明与电子签名页面
     */
    public function jobStep4()
    {
        // 必须先完成step3
        if (!session('step3_completed')) {
            return redirect()->route('job.step3')->withErrors(['请先完成上一步']);
        }
        // 获取step1和step2数据
        $step1 = session('current_job_id') ? [
            'job_id' => session('current_job_id'),
            'job_code' => session('current_job_code'),
            'job_title' => session('current_job_title'),
        ] : [];
        $step2 = session('step2_data', []);
        // 返回声明与电子签名页面
        return view('job_page.submit', compact('step1', 'step2'));
    }

    /**
     * step4表单提交，保存所有数据到数据库
     */
    public function jobStep4Submit(Request $request)
    {
        // 检查必须先完成step3
        if (!session('step3_completed')) {
            return redirect()->route('job.step3')->withErrors(['请先完成上一步']);
        }

        // 验证电子签名
        $validator = \Validator::make($request->all(), [
            'signature' => 'required',
            'signature_name' => 'required|string|max:255',
            'signature_date' => 'required|date',
            'declaration' => 'required|accepted',
        ], [
            'signature.required' => '请提供电子签名',
            'signature_name.required' => '请提供您的姓名',
            'signature_date.required' => '请提供签名日期',
            'signature_date.date' => '签名日期格式不正确',
            'declaration.required' => '请同意声明',
            'declaration.accepted' => '请同意声明'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // 获取step1和step2数据
            $jobId = session('current_job_id');
            $step2Data = session('step2_data', []);

            if (!$jobId || empty($step2Data)) {
                return redirect()->route('job.page')->withErrors(['请重新开始申请流程']);
            }

            $job = AppliedJob::findOrFail($jobId);

            // 创建申请人记录
            $applicant = new Applicant();
            $applicant->job = $jobId;
            $applicant->name = $step2Data['surname'] . ' ' . $step2Data['given_names'];
            $applicant->email = $step2Data['email'];
            $applicant->phone = $step2Data['phone'];
            $applicant->address = $step2Data['residential_address'];
            $applicant->gender = $step2Data['gender'];
            $applicant->dob = $step2Data['date_of_birth'];

            // 保存简历文件信息
            if (isset($step2Data['resume_file'])) {
                $applicant->resume = $step2Data['resume_file'];
            }

            // 收集额外数据
            $extData = $step2Data;
            // 移除已单独保存的字段
            unset($extData['_token']);
            
            // 处理签名图像数据
            $signatureData = $request->input('signature');
            if (!empty($signatureData)) {
                // 记录签名数据大小用于调试
                \Log::info('签名数据大小: ' . strlen($signatureData) . ' bytes');
                \Log::info('POST数据大小: ' . strlen(json_encode($request->all())) . ' bytes');

                try {
                    // 提取base64图像数据
                    if (preg_match('/^data:image\/(\w+);base64,/', $signatureData, $matches)) {
                        // 获取图像类型
                        $imageType = $matches[1];
                        // 去除base64图像头部
                        $base64Data = substr($signatureData, strpos($signatureData, ',') + 1);
                        // 解码base64数据
                        $decodedImage = base64_decode($base64Data);

                        if ($decodedImage !== false) {
                            // 创建唯一文件名
                            $fileName = 'signature_' . time() . '_' . uniqid() . '.' . $imageType;
                            $filePath = 'uploads/signatures/' . $fileName;

                            // 使用Laravel Storage安全地保存文件
                            if (Storage::disk('public')->put($filePath, $decodedImage)) {
                                // 将文件路径添加到额外数据
                                $extData['signature_image'] = $filePath;
                                // 保存签名姓名
                                $extData['signature_name'] = $request->input('signature_name');

                                \Log::info('签名图片保存成功: ' . $filePath);
                            } else {
                                \Log::error('签名图片保存失败: ' . $filePath);
                                return redirect()->back()
                                    ->withErrors(['signature' => '签名图片保存失败，请重试'])
                                    ->withInput();
                            }
                        } else {
                            \Log::error('签名数据base64解码失败');
                            return redirect()->back()
                                ->withErrors(['signature' => '签名数据格式错误'])
                                ->withInput();
                        }
                    } else {
                        \Log::error('签名数据格式不正确: ' . substr($signatureData, 0, 50));
                        return redirect()->back()
                            ->withErrors(['signature' => '签名数据格式不正确'])
                            ->withInput();
                    }
                } catch (\Exception $e) {
                    \Log::error('处理签名图片时出错: ' . $e->getMessage());
                    return redirect()->back()
                        ->withErrors(['signature' => '处理签名时出错，请重试'])
                        ->withInput();
                }
            }
            
            // 添加签名日期到额外数据中
            $extData['signature_date'] = $request->input('signature_date');
            
            // 处理并保存问题答案
            $applicant->question = isset($extData['question']) ? json_encode($extData['question'], JSON_UNESCAPED_UNICODE) : null;
            $applicant->ext_data = !empty($extData) ? json_encode($extData, JSON_UNESCAPED_UNICODE) : null;
            $applicant->save();

            // 清理session
            session()->forget(['current_job_id', 'current_job_code', 'current_job_title', 'step1_completed', 'step2_data', 'step2_completed', 'step3_completed', 'resume_file']);

            return redirect()->route('job.page')->with('success', '提交成功，感谢您的申请！');
        } catch (\Exception $e) {
            // 移除throw $e，记录错误到日志
            \Log::error('申请提交错误: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->back()
                ->withErrors(['general' => '保存失败：' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * 处理简历PDF上传
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadResume(Request $request)
    {
        try {
            $validator = \Validator::make($request->all(), [
                'file' => 'required|mimes:pdf|max:51200', // 限制为PDF文件，最大50MB
            ], [
                'file.required' => '请选择要上传的文件',
                'file.mimes' => '只能上传PDF文件',
                'file.max' => '文件大小不能超过50MB',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 400);
            }

            if ($request->hasFile('file')) {
                $file = $request->file('file');

                // 检查文件是否有效
                if (!$file->isValid()) {
                    return response()->json([
                        'success' => false,
                        'message' => '文件上传过程中出现错误，请重试'
                    ], 400);
                }

                // 清理旧文件（如果存在）
                if (session('resume_file')) {
                    $oldFilePath = storage_path('app/public/' . session('resume_file'));
                    if (file_exists($oldFilePath)) {
                        unlink($oldFilePath);
                    }
                }

                $fileName = time() . '_' . $file->getClientOriginalName();

                // 确保目录存在
                $directory = storage_path('app/public/uploads/resumes');
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                // 存储文件到storage/app/public/uploads/resumes目录下
                $filePath = $file->storeAs('uploads/resumes', $fileName, 'public');

                // 验证文件是否成功保存
                if (!$filePath) {
                    return response()->json([
                        'success' => false,
                        'message' => '文件保存失败，请重试'
                    ], 500);
                }

                // 将文件路径保存到session，以便在提交表单时使用
                session(['resume_file' => $filePath]);

                return response()->json([
                    'success' => true,
                    'message' => '文件上传成功',
                    'filepath' => $filePath,
                    'filename' => $file->getClientOriginalName()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => '上传失败，没有找到文件'
            ], 400);

        } catch (\Exception $e) {
            \Log::error('Resume upload error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '文件上传失败，请重试'
            ], 500);
        }
    }
}
