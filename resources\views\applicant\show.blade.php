@extends('layouts.app')
@section('page-title')
    {{ __('Applicant') }}
@endsection
@section('breadcrumb')
    <ul class="breadcrumb mb-0">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('applicant.index') }}">{{ __('Applicant') }}</a>
        </li>
        <li class="breadcrumb-item active">
            <a href="#">{{ __('Detail') }}</a>
        </li>
    </ul>
@endsection

@push('script-page')
    <script src="{{ asset('assets/js/plugins/star-rating.min.js') }}"></script>
@endpush

@push('css-page')
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/star-rating.min.css') }}" />
    <style>
        :root {
            --gl-star-empty: url('../../images/rating/star-empty.svg');
            --gl-star-full: url('../../images/rating/star-full.svg');
            --gl-star-size: 20px;
        }

        .gl-star-rating [data-value]:not(.gl-active) .gl-emote-bg {
            fill: #dcdce6;
        }

        .gl-active svg.feather {
            fill: var(--bs-danger);
            stroke: var(--bs-danger);
        }
    </style>
@endpush

@section('card-action-btn')
    @if (Gate::check('create employee') || Gate::check('delete employee'))
        @if ($applicant->is_hire == 0)
            <a href="#" class="btn btn-success customModal me-2" data-size="md"
                data-url="{{ route('create.applicant.hire', $applicant->id) }}"
                data-title="{{ __('Add to Employee List') }}">
                {{ __('Add to Employee List') }}
            </a>
        @else
            {!! Form::open(['method' => 'POST', 'route' => ['hire.action', $applicant->id]]) !!}
            <button type="submit" href="{{ route('hire.action', $applicant->id) }}" class="btn btn-danger me-2">
                {{ __('Remove From Employee List') }}
            </button>
            {!! Form::close() !!}
        @endif
    @endif



@endsection

@section('content')
    <div class="row">
        <div class="col-12">

            <div class="d-print-none card mb-3">
                <div class="card-body p-3">
                    <ul class="list-inline ms-auto mb-0 d-flex justify-content-end flex-wrap">


                        @if (Gate::check('create employee') || Gate::check('delete employee'))
                            <li class="list-inline-item align-bottom me-2">
                                @if ($applicant->is_hire == 0)
                                    <a href="#" class="avtar avtar-s btn-link-secondary customModal " data-size="md"
                                        data-url="{{ route('create.applicant.hire', $applicant->id) }}"
                                        data-title="{{ __('Add to Employee List') }}" data-bs-toggle="tooltip"
                                        data-bs-original-title="{{ __('Add to Employee List') }}">
                                        <i class="ph-duotone ph-plus-circle f-20"></i>
                                    </a>
                                @else
                                    {!! Form::open(['method' => 'POST', 'route' => ['hire.action', $applicant->id]]) !!}
                                    <div class="avtar avtar-s btn-link-secondary">
                                        <button class="border-0 bg-transparent text-secondary" data-bs-toggle="tooltip"
                                            data-bs-original-title=" {{ __('Remove From Employee List') }}">
                                            <i class="ph-duotone ph-trash f-20"></i>
                                        </button>
                                    </div>
                                    {!! Form::close() !!}
                                @endif
                            </li>
                        @endif
                        @if (Gate::check('create archive applicant') || Gate::check('delete archive applicant'))
                            <li class="list-inline-item align-bottom me-2">
                                {!! Form::open([
                                    'method' => 'POST',
                                    'route' => ['archive.action', $applicant->id],
                                    'id' => 'archive-applicant-' . $applicant->id,
                                ]) !!}
                                @if ($applicant->is_archive == 0)
                                <div class="avtar avtar-s btn-link-secondary">
                                    <button class="border-0 bg-transparent text-secondary" data-bs-toggle="tooltip"
                                        data-bs-original-title=" {{ __('Add to Archive List') }}">
                                        <i class="ph-duotone ph-archive-box f-20"></i>
                                    </button>
                                </div>
                                @else
                                <div class="avtar avtar-s btn-link-secondary">
                                    <button class="border-0 bg-transparent text-secondary" data-bs-toggle="tooltip"
                                        data-bs-original-title=" {{ __('Remove From Archive List') }}">
                                        <i class="ph-duotone ph-arrow-square-up f-20"></i>
                                    </button>
                                </div>
                                @endif

                                {!! Form::close() !!}
                            </li>
                        @endif

                        @if (\App\Models\ApplicantVote::canVote(Auth::user()))
                            <li class="list-inline-item align-bottom me-2">
                                <a href="{{ route('applicant.votes.show', $applicant->id) }}" class="avtar avtar-s btn-link-secondary"
                                    data-bs-toggle="tooltip" data-bs-original-title="{{ __('Votes') }}">
                                    <i class="ph-duotone ph-thumbs-up f-20"></i>
                                </a>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('Voting Assessment Area') }}</h5>
                                </div>
                                <div class="card-body">
                                    @if (\App\Models\ApplicantVote::canVote(Auth::user()))
                                        @php
                                            $userVote = $applicant->getUserVote(Auth::id());
                                            $voteValue = $userVote ? $userVote->vote_value : 0;
                                            $voteComment = $userVote ? $userVote->comment : '';
                                        @endphp
                                        
                                        {{ Form::open(['route' => $userVote ? ['applicant.votes.update', $applicant->id] : ['applicant.votes.store', $applicant->id], 'method' => $userVote ? 'PUT' : 'POST']) }}
                                        
                                        <div class="form-group mb-4">
                                            {{ Form::label('vote_value', __('Your Vote'), ['class' => 'form-label']) }}
                                            <div class="d-flex justify-content-around py-3">
                                                <div class="form-check form-check-inline">
                                                    {{ Form::radio('vote_value', \App\Models\ApplicantVote::VOTE_SUPPORT, $voteValue == \App\Models\ApplicantVote::VOTE_SUPPORT, ['class' => 'form-check-input', 'id' => 'vote_support']) }}
                                                    {{ Form::label('vote_support', __('Support'), ['class' => 'form-check-label text-success']) }}
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    {{ Form::radio('vote_value', \App\Models\ApplicantVote::VOTE_NEUTRAL, $voteValue == \App\Models\ApplicantVote::VOTE_NEUTRAL, ['class' => 'form-check-input', 'id' => 'vote_neutral']) }}
                                                    {{ Form::label('vote_neutral', __('Neutral'), ['class' => 'form-check-label text-warning']) }}
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    {{ Form::radio('vote_value', \App\Models\ApplicantVote::VOTE_OPPOSE, $voteValue == \App\Models\ApplicantVote::VOTE_OPPOSE, ['class' => 'form-check-input', 'id' => 'vote_oppose']) }}
                                                    {{ Form::label('vote_oppose', __('Oppose'), ['class' => 'form-check-label text-danger']) }}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group mb-4">
                                            {{ Form::label('comment', __('Vote Comment'), ['class' => 'form-label']) }}
                                            {{ Form::textarea('comment', $voteComment, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Add your comment or reason for voting...')]) }}
                                        </div>
                                        
                                        <div class="form-group mb-0 text-end">
                                            <button type="submit" class="btn btn-primary">{{ $userVote ? __('Update Vote') : __('Submit Vote') }}</button>
                                            
                                            @if ($userVote)
                                                <a href="#" class="btn btn-danger ms-2" onclick="event.preventDefault(); document.getElementById('delete-vote-form').submit();">
                                                    {{ __('Remove Vote') }}
                                                </a>
                                                {{ Form::close() }}
                                                
                                                {{ Form::open(['route' => ['applicant.votes.destroy', $applicant->id], 'method' => 'DELETE', 'id' => 'delete-vote-form']) }}
                                                {{ Form::close() }}
                                            @else
                                                {{ Form::close() }}
                                            @endif
                                        </div>
                                    @else
                                        <div class="alert alert-info mb-0">
                                            {{ __('Only Owner, HOD and Manager roles can vote for applicants.') }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if (!empty($comments))
                                <div class="card border">
                                    <div class="card-header">
                                        <h5>{{ __('Comments') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="blgcomment-list">
                                            @foreach ($comments as $comment)
                                                <div class="row">
                                                    <div class="col-sm-auto mb-3 mb-sm-0">
                                                        <div class="d-sm-inline-block d-flex align-items-center">
                                                            <img class="media-object wid-60 img-radius"
                                                                src="{{ !empty($comment->user) && !empty($comment->user->profile) ? asset(Storage::url('upload/profile')) . '/' . $comment->user->profile : asset(Storage::url('upload/profile')) . '/avatar.png' }}"
                                                                alt=" ">
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="popup-trigger">
                                                            <div class="h5 font-weight-bold">
                                                                {{ !empty($comment->user) ? $comment->user->name : '' }}
                                                                <span class="comment-time float-end">
                                                                    {{ dateFormat($comment->created_at) }}</span>
                                                            </div>
                                                            <div class="help-md-hidden">
                                                                <div class="bg-body mb-3 p-3">
                                                                    <p class="mb-0">
                                                                        {{ $comment->comment }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- 投票统计 -->
                            <div class="mt-5">
                                <h5 class="text-primary mb-3"><i class="fa fa-chart-bar"></i> {{ __('Vote Statistics') }}</h5>
                                @php
                                    $voteStats = $applicant->voteStats();
                                @endphp
                                
                                <div class="row text-center mb-4">
                                    <div class="col-4">
                                        <div class="p-3 bg-light rounded">
                                            <h4 class="text-success mb-1">{{ $voteStats['support'] }}</h4>
                                            <p class="text-muted mb-0">{{ __('Support') }}</p>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="p-3 bg-light rounded">
                                            <h4 class="text-danger mb-1">{{ $voteStats['oppose'] }}</h4>
                                            <p class="text-muted mb-0">{{ __('Oppose') }}</p>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="p-3 bg-light rounded">
                                            <h4 class="text-warning mb-1">{{ $voteStats['neutral'] }}</h4>
                                            <p class="text-muted mb-0">{{ __('Neutral') }}</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h6>{{ __('Support Rate') }}: {{ $voteStats['support_rate'] }}%</h6>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ $voteStats['support_rate'] }}%" aria-valuenow="{{ $voteStats['support_rate'] }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="mt-4 text-end">
                                    <a href="{{ route('applicant.votes.show', $applicant->id) }}" class="btn btn-info">
                                        {{ __('View All Votes') }} <i class="fa fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                    
                    <!-- 岗位基本介绍 -->
                    @if(!empty($applicant->appliedJob))
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('Job Description') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-briefcase"></i> {{ $applicant->appliedJob->job_title }}</h5>
                                        <div class="row">
                                            <div class="col-md-4 mb-2"><b>Job Code:</b> {{ $applicant->appliedJob->code }}</div>
                                            <div class="col-md-4 mb-2"><b>Position:</b> {{ $applicant->appliedJob->position }}</div>
                                            <div class="col-md-4 mb-2"><b>Status:</b> 
                                                <span class="badge {{ $applicant->appliedJob->status == 1 ? 'bg-success' : 'bg-danger' }}">
                                                    {{ $applicant->appliedJob->status == 1 ? 'Active' : 'Closed' }}
                                                </span>
                                            </div>
                                            <div class="col-md-4 mb-2"><b>Type:</b> 
                                                @if(!empty($applicant->appliedJob->jobType))
                                                    {{ $applicant->appliedJob->jobType->type }}
                                                @else
                                                    -
                                                @endif
                                            </div>
                                            <div class="col-md-4 mb-2"><b>Category:</b> 
                                                @if($applicant->appliedJob->job_category == 1)
                                                    Teaching
                                                @elseif($applicant->appliedJob->job_category == 2)
                                                    Non-teaching
                                                @else
                                                    -
                                                @endif
                                            </div>
                                            <div class="col-md-4 mb-2"><b>Location:</b> 
                                                @if(!empty($applicant->appliedJob->jobLocation))
                                                    {{ $applicant->appliedJob->jobLocation->location }}
                                                @else
                                                    -
                                                @endif
                                            </div>
                                            <div class="col-md-4 mb-2"><b>Start Date:</b> {{ $applicant->appliedJob->start_date }}</div>
                                            <div class="col-md-4 mb-2"><b>End Date:</b> {{ $applicant->appliedJob->end_date }}</div>
                                            <div class="col-md-4 mb-2"><b>Salary Range:</b> {{ $applicant->appliedJob->min_salary }} - {{ $applicant->appliedJob->max_salary }}</div>
                                            <div class="col-md-4 mb-2"><b>Salary Period:</b> 
                                                @if($applicant->appliedJob->salary_period == 'monthly')
                                                    Monthly
                                                @elseif($applicant->appliedJob->salary_period == 'yearly')
                                                    Yearly
                                                @else
                                                    {{ $applicant->appliedJob->salary_period }}
                                                @endif
                                            </div>
                                            <div class="col-md-4 mb-2"><b>Experience Required:</b> {{ $applicant->appliedJob->min_experience }} - {{ $applicant->appliedJob->max_experience }} years</div>
                                        </div>
                                    </div>

                                    <!-- 岗位描述 -->
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-file-text"></i> {{ __('Job Description') }}</h5>
                                        <div class="bg-light p-3 rounded">
                                            {!! $applicant->appliedJob->job_description !!}
                                        </div>
                                    </div>

                                    <!-- 岗位要求 -->
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-check-circle"></i> {{ __('Job Requirements') }}</h5>
                                        <div class="bg-light p-3 rounded">
                                            {!! $applicant->appliedJob->job_requirement !!}
                                        </div>
                                    </div>

                                    <!-- 技能要求 -->
                                    @if(!empty($applicant->appliedJob->job_skill))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-star"></i> {{ __('Required Skills') }}</h5>
                                        <div class="d-flex flex-wrap gap-2">
                                            @foreach(explode(',', $applicant->appliedJob->job_skill) as $skillId)
                                                @php
                                                    $skill = $skills->where('id', $skillId)->first();
                                                @endphp
                                                @if($skill)
                                                    <span class="badge bg-info">{{ $skill->skill }}</span>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <!-- 申请人详细信息 -->
                    @if(!empty($applicant->ext_data))
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('Applicant Details') }}</h5>
                                </div>
                                <div class="card-body">
                                    <!-- 个人基本信息 -->
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-user"></i> {{ __('Basic Information') }}</h5>
                                        <div class="row">
                                            <div class="col-md-4 mb-2"><b>Surname:</b> {{ $applicant->ext_data['surname'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Given Names:</b> {{ $applicant->ext_data['given_names'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Chinese Name:</b> {{ $applicant->ext_data['name_in_chinese'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Gender:</b> {{ $applicant->ext_data['gender'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Date of Birth:</b> {{ $applicant->ext_data['date_of_birth'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Place of Birth:</b> {{ $applicant->ext_data['place_of_birth'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>HKID No:</b> {{ $applicant->ext_data['hkid_no'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Passport No:</b> {{ $applicant->ext_data['passport_number'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Place of Issue:</b> {{ $applicant->ext_data['place_of_issue'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Nationality:</b> {{ $applicant->ext_data['nationality'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Religion:</b> {{ $applicant->ext_data['religion'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Residential Address:</b> {{ $applicant->ext_data['residential_address'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Phone:</b> {{ $applicant->ext_data['phone'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Email:</b> {{ $applicant->ext_data['email'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Registered Teacher No:</b> {{ $applicant->ext_data['registered_teacher_no'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Availability:</b> {{ $applicant->ext_data['availability'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Current Salary:</b> {{ $applicant->ext_data['current_salary'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Salary Expected:</b> {{ $applicant->ext_data['salary_expected'] ?? '-' }}</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 学历 -->
                                    @if(!empty($applicant->ext_data['academic_attainment']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-graduation-cap"></i> {{ __('Education') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Issuing Authority</th>
                                                        <th>Qualification</th>
                                                        <th>Issue Date</th>
                                                        <th>Major Subjects</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['academic_attainment'] as $academic)
                                                        @if(!empty($academic['issuing_authority']) || !empty($academic['qualification']))
                                                        <tr>
                                                            <td>{{ $academic['issuing_authority'] ?? '-' }}</td>
                                                            <td>{{ $academic['qualification'] ?? '-' }}</td>
                                                            <td>{{ $academic['issue_date'] ?? '-' }}</td>
                                                            <td>{{ $academic['major_subjects'] ?? '-' }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif

                                    <!-- 问题列表 -->
                                    @if(!empty($applicant->ext_data['full_questions']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-question-circle"></i> {{ __('Question List') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Question</th>
                                                        <th>Answer</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['full_questions'] as $question)
                                                    <tr>
                                                        <td>{{ $question['question'] }}</td>
                                                        <td>{{ $question['answer'] }}</td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif
                                    
                                    <!-- 专业资质 -->
                                    @if(!empty($applicant->ext_data['professional_qualification']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-certificate"></i> {{ __('Professional Qualification') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Issuing Authority</th>
                                                        <th>Qualification</th>
                                                        <th>Status</th>
                                                        <th>Level</th>
                                                        <th>Issue Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['professional_qualification'] as $qualification)
                                                        @if(!empty($qualification['issuing_authority']) || !empty($qualification['qualification']))
                                                        <tr>
                                                            <td>{{ $qualification['issuing_authority'] ?? '-' }}</td>
                                                            <td>{{ $qualification['qualification'] ?? '-' }}</td>
                                                            <td>{{ $qualification['status'] == '0' ? 'Pass' : 'Fail' }}</td>
                                                            <td>{{ $qualification['level'] ?? '-' }}</td>
                                                            <td>{{ $qualification['issue_date'] ?? '-' }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif
                                    
                                    <!-- 特殊教育 -->
                                    @if(!empty($applicant->ext_data['special_education']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-book"></i> {{ __('Special Education') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Issuing Authority</th>
                                                        <th>Qualification</th>
                                                        <th>Status</th>
                                                        <th>Level</th>
                                                        <th>Issue Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['special_education'] as $education)
                                                        @if(!empty($education['issuing_authority']) || !empty($education['qualification']))
                                                        <tr>
                                                            <td>{{ $education['issuing_authority'] ?? '-' }}</td>
                                                            <td>{{ $education['qualification'] ?? '-' }}</td>
                                                            <td>{{ $education['status'] == '0' ? 'Pass' : 'Fail' }}</td>
                                                            <td>{{ $education['level'] ?? '-' }}</td>
                                                            <td>{{ $education['issue_date'] ?? '-' }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif
                                    
                                    <!-- 工作经验 -->
                                    @if(!empty($applicant->ext_data['work_experience']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-briefcase"></i> {{ __('Work Experience') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Employer</th>
                                                        <th>Position</th>
                                                        <th>Start Date</th>
                                                        <th>End Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['work_experience'] as $experience)
                                                        @if(!empty($experience['employer']) || !empty($experience['position']))
                                                        <tr>
                                                            <td>{{ $experience['employer'] ?? '-' }}</td>
                                                            <td>{{ $experience['position'] ?? '-' }}</td>
                                                            <td>{{ $experience['date_from'] ?? '-' }}</td>
                                                            <td>{{ $experience['date_to'] ?? '-' }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif
                                    
                                    <!-- 推荐人 -->
                                    @if(!empty($applicant->ext_data['referee']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-users"></i> {{ __('Referee') }}</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Company</th>
                                                        <th>Position</th>
                                                        <th>Contact Number</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($applicant->ext_data['referee'] as $referee)
                                                        @if(!empty($referee['name']) || !empty($referee['company']))
                                                        <tr>
                                                            <td>{{ $referee['name'] ?? '-' }}</td>
                                                            <td>{{ $referee['company'] ?? '-' }}</td>
                                                            <td>{{ $referee['position'] ?? '-' }}</td>
                                                            <td>{{ $referee['contact_number'] ?? '-' }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @endif
                                    
                                    <!-- 电子签名 -->
                                    @if(!empty($applicant->ext_data['signature_image']))
                                    <div class="mb-4">
                                        <h5 class="text-primary mb-3"><i class="fa fa-signature"></i> {{ __('Signature') }}</h5>
                                        <div class="row">
                                            <div class="col-md-4 mb-2"><b>Signer:</b> {{ $applicant->ext_data['signature_name'] ?? '-' }}</div>
                                            <div class="col-md-4 mb-2"><b>Signature Date:</b> {{ $applicant->ext_data['signature_date'] ?? '-' }}</div>
                                        </div>
                                        <div class="signature-image mt-2">
                                            <img src="{{ asset('storage/app/public/' . $applicant->ext_data['signature_image']) }}" alt="Signature" class="img-fluid" style="max-height: 150px; border: 1px solid #eee; padding: 10px;">
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                </div>

            </div>

        </div>

    </div>

    {{-- ext_data 附加信息展示区域 --}}
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Additional Information') }}</h5>
                </div>
                <div class="card-body">
                    
                    {{-- 学术成就 Academic Attainment --}}
                    @if(!empty($applicantData['academic_attainment']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-graduation-cap me-2"></i>{{ __('Academic Attainment') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Issuing Authority') }}</th>
                                            <th>{{ __('Qualification') }}</th>
                                            <th>{{ __('Issue Date') }}</th>
                                            <th>{{ __('Major Subjects') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['academic_attainment'] as $academic)
                                            @if(!empty($academic['issuing_authority']) || !empty($academic['qualification']))
                                            <tr>
                                                <td>{{ $academic['issuing_authority'] ?? '-' }}</td>
                                                <td>{{ $academic['qualification'] ?? '-' }}</td>
                                                <td>{{ $academic['issue_date'] ?? '-' }}</td>
                                                <td>{{ $academic['major_subjects'] ?? '-' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    {{-- 专业资格 Professional Qualification --}}
                    @if(!empty($applicantData['professional_qualification']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-certificate me-2"></i>{{ __('Professional Qualification') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Issuing Authority') }}</th>
                                            <th>{{ __('Qualification') }}</th>
                                            <th>{{ __('Level') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Issue Date') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['professional_qualification'] as $qual)
                                            @if(!empty($qual['issuing_authority']) || !empty($qual['qualification']))
                                            <tr>
                                                <td>{{ $qual['issuing_authority'] ?? '-' }}</td>
                                                <td>{{ $qual['qualification'] ?? '-' }}</td>
                                                <td>{{ $qual['level'] ?? '-' }}</td>
                                                <td>{{ $qual['status'] == '0' ? __('Active') : __('Inactive') }}</td>
                                                <td>{{ $qual['issue_date'] ?? '-' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    {{-- 特殊教育 Special Education --}}
                    @if(!empty($applicantData['special_education']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-student me-2"></i>{{ __('Special Education') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Issuing Authority') }}</th>
                                            <th>{{ __('Qualification') }}</th>
                                            <th>{{ __('Level') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Issue Date') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['special_education'] as $edu)
                                            @if(!empty($edu['issuing_authority']) || !empty($edu['qualification']))
                                            <tr>
                                                <td>{{ $edu['issuing_authority'] ?? '-' }}</td>
                                                <td>{{ $edu['qualification'] ?? '-' }}</td>
                                                <td>{{ $edu['level'] ?? '-' }}</td>
                                                <td>{{ $edu['status'] == '0' ? __('Active') : __('Inactive') }}</td>
                                                <td>{{ $edu['issue_date'] ?? '-' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    {{-- 工作经验 Work Experience --}}
                    @if(!empty($applicantData['work_experience']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-briefcase me-2"></i>{{ __('Work Experience') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Employer') }}</th>
                                            <th>{{ __('Position') }}</th>
                                            <th>{{ __('Period') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['work_experience'] as $exp)
                                            @if(!empty($exp['employer']) || !empty($exp['position']))
                                            <tr>
                                                <td>{{ $exp['employer'] ?? '-' }}</td>
                                                <td>{{ $exp['position'] ?? '-' }}</td>
                                                <td>
                                                    @if(!empty($exp['date_from']) || !empty($exp['date_to']))
                                                        {{ $exp['date_from'] ?? '' }} - {{ $exp['date_to'] ?? __('Present') }}
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    {{-- 推荐人 Referee --}}
                    @if(!empty($applicantData['referee']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-users me-2"></i>{{ __('Referees') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Name') }}</th>
                                            <th>{{ __('Company') }}</th>
                                            <th>{{ __('Position') }}</th>
                                            <th>{{ __('Contact Number') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['referee'] as $referee)
                                            @if(!empty($referee['name']) || !empty($referee['company']))
                                            <tr>
                                                <td>{{ $referee['name'] ?? '-' }}</td>
                                                <td>{{ $referee['company'] ?? '-' }}</td>
                                                <td>{{ $referee['position'] ?? '-' }}</td>
                                                <td>{{ $referee['contact_number'] ?? '-' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    {{-- 问题回答 Questions --}}
                    @if(!empty($applicantData['question']))
                    <div class="card border mb-3">
                        <div class="card-header">
                            <h5><i class="ph-duotone ph-question me-2"></i>{{ __('Application Questions') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Question ID') }}</th>
                                            <th>{{ __('Answer') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($applicantData['question'] as $qid => $answer)
                                            <tr>
                                                <td>{{ $qid }}</td>
                                                <td>{{ $answer }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection
