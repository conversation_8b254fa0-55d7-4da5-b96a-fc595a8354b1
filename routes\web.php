<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\AuthPageController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\NoticeBoardController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\FAQController;
use App\Http\Controllers\HomePageController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OTPController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentController;

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\SkillController;
use App\Http\Controllers\TypeController;
use App\Http\Controllers\StageController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\AppliedJobController;
use App\Http\Controllers\ApplicantController;
use App\Http\Controllers\InterviewController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

require __DIR__ . '/auth.php';
Route::get('register', function () {
    abort(404);
});
Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    } else {
        return redirect()->route('login');
    }
});
Route::get('home', [HomeController::class, 'index'])->name('home')->middleware(
    [

        'XSS',
    ]
);
Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard')->middleware(
    [

        'XSS',
    ]
);

//-------------------------------User-------------------------------------------

Route::resource('users', UserController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

// 账户锁定管理路由
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {
        Route::post('users/{id}/unlock', [UserController::class, 'unlockAccount'])->name('users.unlock');
        Route::post('users/{id}/reset-attempts', [UserController::class, 'resetFailedAttempts'])->name('users.reset.attempts');
    }
);

Route::get('login/otp', [OTPController::class, 'show'])->name('otp.show')->middleware(
    [

        'XSS',
    ]
);
Route::post('login/otp', [OTPController::class, 'check'])->name('otp.check')->middleware(
    [

        'XSS',
    ]
);
Route::get('login/2fa/disable', [OTPController::class, 'disable'])->name('2fa.disable')->middleware(['XSS',]);

//-------------------------------Subscription-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {

        Route::resource('subscriptions', SubscriptionController::class);
        Route::get('coupons/history', [CouponController::class, 'history'])->name('coupons.history');
        Route::delete('coupons/history/{id}/destroy', [CouponController::class, 'historyDestroy'])->name('coupons.history.destroy');
        Route::get('coupons/apply', [CouponController::class, 'apply'])->name('coupons.apply');
        Route::resource('coupons', CouponController::class);
        Route::get('subscription/transaction', [SubscriptionController::class, 'transaction'])->name('subscription.transaction');
    }
);

//-------------------------------Subscription Payment-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {

        Route::post('subscription/{id}/stripe/payment', [SubscriptionController::class, 'stripePayment'])->name('subscription.stripe.payment');
    }
);
//-------------------------------Settings-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {
        Route::get('settings', [SettingController::class, 'index'])->name('setting.index');

        Route::post('settings/account', [SettingController::class, 'accountData'])->name('setting.account');
        Route::delete('settings/account/delete', [SettingController::class, 'accountDelete'])->name('setting.account.delete');
        Route::post('settings/password', [SettingController::class, 'passwordData'])->name('setting.password');
        Route::post('settings/general', [SettingController::class, 'generalData'])->name('setting.general');
        Route::post('settings/smtp', [SettingController::class, 'smtpData'])->name('setting.smtp');
        Route::get('settings/smtp-test', [SettingController::class, 'smtpTest'])->name('setting.smtp.test');
        Route::post('settings/smtp-test', [SettingController::class, 'smtpTestMailSend'])->name('setting.smtp.testing');
        Route::post('settings/payment', [SettingController::class, 'paymentData'])->name('setting.payment');
        Route::post('settings/site-seo', [SettingController::class, 'siteSEOData'])->name('setting.site.seo');
        Route::post('settings/google-recaptcha', [SettingController::class, 'googleRecaptchaData'])->name('setting.google.recaptcha');
        Route::post('settings/company', [SettingController::class, 'companyData'])->name('setting.company');
        Route::post('settings/2fa', [SettingController::class, 'twofaEnable'])->name('setting.twofa.enable');

        Route::get('footer-setting', [SettingController::class, 'footerSetting'])->name('footerSetting');
        Route::post('settings/footer', [SettingController::class, 'footerData'])->name('setting.footer');

        Route::get('language/{lang}', [SettingController::class, 'lanquageChange'])->name('language.change');
        Route::post('theme/settings', [SettingController::class, 'themeSettings'])->name('theme.settings');

        // ================= 个人设置 Google 账号绑定/解绑路由 =================
        // 跳转到 Google 认证（个人绑定）
        Route::get('settings/google/bind', [App\Http\Controllers\Auth\GoogleController::class, 'bindGoogle'])->name('setting.google.bind');
        // Google 认证回调（个人绑定）
        Route::get('settings/google/callback', [App\Http\Controllers\Auth\GoogleController::class, 'bindGoogleCallback'])->name('setting.google.callback');
        // 解绑 Google 账号
        Route::post('settings/google/unbind', [App\Http\Controllers\Auth\GoogleController::class, 'unbindGoogle'])->name('setting.google.unbind');
    }
);


//-------------------------------Role & Permissions-------------------------------------------
Route::resource('permission', PermissionController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

Route::resource('role', RoleController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Note-------------------------------------------
Route::resource('note', NoticeBoardController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Contact-------------------------------------------
Route::resource('contact', ContactController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------logged History-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {

        Route::get('logged/history', [UserController::class, 'loggedHistory'])->name('logged.history');
        Route::get('logged/{id}/history/show', [UserController::class, 'loggedHistoryShow'])->name('logged.history.show');
        Route::delete('logged/{id}/history', [UserController::class, 'loggedHistoryDestroy'])->name('logged.history.destroy');
    }
);


//-------------------------------Plan Payment-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {
        Route::post('subscription/{id}/bank-transfer', [PaymentController::class, 'subscriptionBankTransfer'])->name('subscription.bank.transfer');
        Route::get('subscription/{id}/bank-transfer/action/{status}', [PaymentController::class, 'subscriptionBankTransferAction'])->name('subscription.bank.transfer.action');
        Route::post('subscription/{id}/paypal', [PaymentController::class, 'subscriptionPaypal'])->name('subscription.paypal');
        Route::get('subscription/{id}/paypal/{status}', [PaymentController::class, 'subscriptionPaypalStatus'])->name('subscription.paypal.status');
        Route::post('subscription/{id}/{user_id}/manual-assign-package', [PaymentController::class, 'subscriptionManualAssignPackage'])->name('subscription.manual_assign_package');
        Route::get('subscription/flutterwave/{sid}/{tx_ref}', [PaymentController::class, 'subscriptionFlutterwave'])->name('subscription.flutterwave');
    }
);

//-------------------------------Notification-------------------------------------------
Route::resource('notification', NotificationController::class)->middleware(
    [
        'auth',
        'XSS',

    ]
);

Route::get('email-verification/{token}', [VerifyEmailController::class, 'verifyEmail'])->name('email-verification')->middleware(
    [
        'XSS',
    ]
);

//-------------------------------FAQ-------------------------------------------
Route::resource('FAQ', FAQController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Home Page-------------------------------------------
Route::resource('homepage', HomePageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);
//-------------------------------FAQ-------------------------------------------
Route::resource('pages', PageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Auth page-------------------------------------------
Route::resource('authPage', AuthPageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Job Category-------------------------------------------
Route::resource('category', CategoryController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);
//-------------------------------Job Location-------------------------------------------
Route::resource('location', LocationController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);
//-------------------------------Job Skill-------------------------------------------
Route::resource('skill', SkillController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Job Type-------------------------------------------
Route::resource('type', TypeController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);
//-------------------------------Job Stage-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {
        Route::post('stage/order', [StageController::class, 'stageOrder'])->name('stage.order');
        Route::resource('stage', StageController::class);
    }
);

//-------------------------------Questions-------------------------------------------
Route::resource('question', QuestionController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);



//-------------------------------Applied Jobs-------------------------------------------


Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {
        Route::get('applied-job/{id}/edit', [AppliedJobController::class, 'edit'])->name('applied-job.edit');
        Route::get('applied-job/{id}/show', [AppliedJobController::class, 'show'])->name('applied-job.show');
        Route::resource('applied-job', AppliedJobController::class);
    }
);

Route::group(
    [
        'middleware' => [
            'XSS',
        ],
    ],
    function () {

       Route::get('job-list', [AppliedJobController::class, 'jobList'])->name('job.list');
        Route::get('job', [AppliedJobController::class, 'jobPage'])->name('job.page');
        Route::post('job/search', [AppliedJobController::class, 'searchJob'])->name('job.search');
        Route::post('job/step1', [AppliedJobController::class, 'processStep1'])->name('job.step1');
        Route::get('job/step2', [\App\Http\Controllers\AppliedJobController::class, 'jobStep2'])->name('job.step2');
        Route::post('job/step2', [\App\Http\Controllers\AppliedJobController::class, 'jobStep2Submit'])->name('job.step2.submit');
        Route::post('job/upload/resume', [\App\Http\Controllers\AppliedJobController::class, 'uploadResume'])->name('job.upload.resume');
        Route::post('job/upload/signature', [\App\Http\Controllers\AppliedJobController::class, 'uploadSignature'])->name('job.upload.signature');
        Route::get('job/step3', [AppliedJobController::class, 'jobStep3'])->name('job.step3');
        Route::post('job/step3', [\App\Http\Controllers\AppliedJobController::class, 'jobStep3Submit'])->name('job.step3.submit');
        Route::get('job/step4', [\App\Http\Controllers\AppliedJobController::class, 'jobStep4'])->name('job.step4');
        Route::post('job/step4', [\App\Http\Controllers\AppliedJobController::class, 'jobStep4Submit'])->name('job.step4.submit');
       Route::get('job/{id}/detail', [AppliedJobController::class, 'appliedJob'])->name('applied-job.detail');
       Route::get('job/{id}/apply', [AppliedJobController::class, 'jobApply'])->name('job.apply');
       Route::post('job/{id}/apply', [AppliedJobController::class, 'applicantData'])->name('applicant.data');
    }
);



//-------------------------------Applicants-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {


        Route::post('applicant/order', [ApplicantController::class, 'order'])->name('applicant.order');
        Route::post('applicant/job', [ApplicantController::class, 'getJobDetails'])->name('get.jobdetail');
        Route::post('applicant/skill', [ApplicantController::class, 'skill'])->name('applicant.skill');
        Route::post('applicant/rating', [ApplicantController::class, 'rating'])->name('applicant.rating');
        Route::post('applicant/{id}/comment', [ApplicantController::class, 'comment'])->name('applicant.comment');
        Route::get('applicant/{id}/show', [ApplicantController::class, 'show'])->name('applicant.show');
        Route::get('applicant/archive', [ApplicantController::class, 'archive'])->name('applicant.archive');
        Route::post('applicant/archive/action/{id}', [ApplicantController::class, 'archiveAction'])->name('archive.action');
        Route::get('applicant/hire', [ApplicantController::class, 'hire'])->name('applicant.hire');
        Route::get('applicant/hire/create/{id}', [ApplicantController::class, 'createHire'])->name('create.applicant.hire');
        Route::post('applicant/hire/action/{id}', [ApplicantController::class, 'hireAction'])->name('hire.action');
        Route::resource('applicant', ApplicantController::class);

        // 申请人投票相关路由
        Route::group(
            [
                'middleware' => [
                    'auth',
                    'XSS',
                ],
            ],
            function () {
                // 查看投票
                Route::get('applicant/{applicantId}/votes', [\App\Http\Controllers\ApplicantVoteController::class, 'showVotes'])->name('applicant.votes.show');
                // 创建投票表单
                Route::get('applicant/{applicantId}/votes/create', [\App\Http\Controllers\ApplicantVoteController::class, 'create'])->name('applicant.votes.create');
                // 保存投票
                Route::post('applicant/{applicantId}/votes', [\App\Http\Controllers\ApplicantVoteController::class, 'store'])->name('applicant.votes.store');
                // 编辑投票表单
                Route::get('applicant/{applicantId}/votes/edit', [\App\Http\Controllers\ApplicantVoteController::class, 'edit'])->name('applicant.votes.edit');
                // 更新投票
                Route::put('applicant/{applicantId}/votes', [\App\Http\Controllers\ApplicantVoteController::class, 'update'])->name('applicant.votes.update');
                // 删除投票
                Route::delete('applicant/{applicantId}/votes', [\App\Http\Controllers\ApplicantVoteController::class, 'destroy'])->name('applicant.votes.destroy');
            }
        );
    }
);


//-------------------------------Interview Schedule-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ],
    function () {

        Route::get('interview/today', [InterviewController::class, 'todayList'])->name('interview.today');
        Route::resource('interview', InterviewController::class);
    }
);


Route::get('page/{slug}', [PageController::class, 'page'])->name('page');
//-------------------------------FAQ-------------------------------------------
Route::impersonate();

// ================= Google 登录相关路由 =================
// 跳转到 Google 认证
Route::get('auth/google', [App\Http\Controllers\Auth\GoogleController::class, 'redirectToGoogle'])->name('auth.google.redirect');
// Google 认证回调
Route::get('auth/google/callback', [App\Http\Controllers\Auth\GoogleController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// 用户组管理路由
Route::prefix('user-groups')->group(function () {
    // Blade页面路由
    Route::get('/', [\App\Http\Controllers\UserGroupController::class, 'index'])->name('user-groups.index'); // 用户组列表页
    Route::get('/create', [\App\Http\Controllers\UserGroupController::class, 'create'])->name('user-groups.create'); // 创建表单
    Route::get('/{id}/edit', [\App\Http\Controllers\UserGroupController::class, 'edit'])->name('user-groups.edit'); // 编辑表单
    Route::get('/{id}/members', [\App\Http\Controllers\UserGroupController::class, 'members'])->name('user-groups.members'); // 成员管理页

    // API接口路由
    Route::post('/', [\App\Http\Controllers\UserGroupController::class, 'store'])->name('user-groups.store'); // 创建
    Route::put('/{id}', [\App\Http\Controllers\UserGroupController::class, 'update'])->name('user-groups.update'); // 更新
    Route::delete('/{id}', [\App\Http\Controllers\UserGroupController::class, 'destroy'])->name('user-groups.destroy'); // 删除
    Route::post('/{id}/restore', [\App\Http\Controllers\UserGroupController::class, 'restore'])->name('user-groups.restore'); // 恢复

    // 用户组成员管理API
    Route::post('/{groupId}/members', [\App\Http\Controllers\UserGroupMemberController::class, 'store'])->name('user-groups.members.store'); // 添加成员
    Route::delete('/{groupId}/members/{userId}', [\App\Http\Controllers\UserGroupMemberController::class, 'destroy'])->name('user-groups.members.destroy'); // 移除成员
});
