<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>香港華人基督教聯會真道書院</title>
    <link href="{{asset('static/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet">
    <link href="{{asset('static/fontawesome/css/fontawesome.css')}}" rel="stylesheet">
    <link href="{{asset('static/fontawesome/css/brands.css')}}" rel="stylesheet">
    <link href="{{asset('static/fontawesome/css/solid.css')}}" rel="stylesheet">
    <link href="{{asset('static/bootstrap/css/bootstrap-datepicker.min.css')}}" rel="stylesheet" />
    <link href="{{asset('static/css/style.css')}}" rel="stylesheet" />
</head>
<body>
<div class="container-fluid">
    <!-- 固定在屏幕底部步骤条 -->
    <div class="step-fixed col-xs-12 col-sm-12 col-md-12 col-lg-7 col-xl-8 col-xxl-8 p-0 position-fixed end-0"
         style="z-index: 99999; bottom: 0.625rem">
        <div class="row">
            <div class="col-4 d-flex justify-content-center align-items-center">
                <a href="#personal" class="d-flex justify-content-center align-items-center step-fixed-item current-step-item">
                    <span class="me-3">1</span>
                    <span>Personal Particulars</span>
                </a>
            </div>
            <div class="col-4 d-flex justify-content-center align-items-center">
                <a href="#qualifications" class="d-flex justify-content-center align-items-center step-fixed-item">
                    <span class="me-3">2</span>
                    <span>Qualifications</span>
                </a>
            </div>
            <div class="col-4 d-flex justify-content-center align-items-center">
                <a href="#work" class="d-flex justify-content-center align-items-center step-fixed-item">
                    <span class="me-3">3</span>
                    <span>Working Experience</span>
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-5 col-xl-4 col-xxl-4 p-0">
            <!-- 左侧侧边栏 -->
            <div class="step-side sticky-lg-top">
                <!-- logo -->
                <div class="header-logo">
                    <img src="{{asset('static/images/B19D5C3408DF47AA72C1B4916D86F1F1.png')}}" alt="" />
                </div>
                <!-- 进度条 -->
                <div class="step-wrap d-flex flex-column justify-content-start align-items-start">
                    <div class="step d-flex flex-row justify-content-start align-items-center">
                        <div class="step-left-round d-flex flex-row justify-content-center align-items-center">
                            <div></div>
                        </div>
                        <div class="d-flex flex-column justify-content-start">
                            <div class="step-number">Step 1</div>
                            <div class="step-title">Job Number & Note Page</div>
                        </div>
                    </div>
                    <div class="step current-step information d-flex flex-row justify-content-start align-items-start">
                        <div class="step-left-round d-flex flex-row justify-content-center align-items-center">
                            <div></div>
                        </div>
                        <div class="d-flex flex-column justify-content-start">
                            <div class="step-number">Step 2</div>
                            <div class="step-title">Information</div>
                            <ul class="information-list">
                                <li>Personal Particulars</li>
                                <li>Qualifications</li>
                                <li>Working Experience</li>
                            </ul>
                        </div>
                    </div>
                    <div class="step d-flex flex-row justify-content-start align-items-center">
                        <div class="step-left-round d-flex flex-row justify-content-center align-items-center">
                            <div></div>
                        </div>
                        <div class="d-flex flex-column justify-content-start">
                            <div class="step-number">Step 3</div>
                            <div class="step-title">Preview and Submit</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-7 col-xl-8 col-xxl-8 p-4">
            <!-- 右侧内容区域 -->
            <!-- 错误提示：后端校验失败时会显示在这里，可手动关闭 -->
            @if ($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                </div>
            @endif
            <form id="step2Form" method="POST" action="{{ route('job.step2.submit') }}">
                @csrf
            <div class="right-content">
                <div class="right-card-public" id="personal">
                    <!-- Personal Particulars -->
                    <div class="right-card-wrap">
                        <div class="content-title d-flex align-items-center">
                            <p>Personal Particulars</p>
                        </div>
                        <div class="right-card-content">
                            <div class="row">
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Surname</label>
                                            <input type="text" name="surname" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Given Names</label>
                                            <input type="text" name="given_names" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Gender</label>
                                        <select class="form-select custom-input name-input" name="gender" aria-label="Default select example">
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                        </select>
                                        <!-- <input type="text" placeholder="" class="form-control custom-input name-input" /> -->
                                    </div>
                                </div>

                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Name in Chinese</label>
                                            <input type="text" name="name_in_chinese" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Date of Birth</label>
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="date_of_birth" class="form-control opacity-input">
                                            <span class="input-group-addon">
														<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
													</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Place of Birth</label>
                                            <input type="text" name="place_of_birth" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>

                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>HKID No.</label>
                                            <input type="text" name="hkid_no" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Passport Number</label>
                                            <input type="text" name="passport_number" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Place of Issue</label>
                                            <input type="text" name="place_of_issue" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>

                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Nationality</label>
                                            <input type="text" name="nationality" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-6 col-xs-6 col-sm-6 col-md-4 col-lg-4 col-xl-4 col-xxl-4">
                                    <div class="personal-item">
                                        <label>Religion(optinal)</label>
                                            <input type="text" name="religion" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="personal-item">
                                        <label>Residential Address</label>
                                            <input type="text" name="residential_address" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>

                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>Phone</label>
                                            <input type="text" name="phone" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>E-mail Address</label>
                                            <input type="text" name="email" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>

                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>Registered Teacher No.</label>
                                            <input type="text" name="registered_teacher_no" placeholder="" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>Availability</label>
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="availability" class="form-control opacity-input">
                                            <span class="input-group-addon">
														<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
													</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>Current Salary $</label>
                                            <input type="text" name="current_salary" placeholder="HKD" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                                <div class="col-12 col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6">
                                    <div class="personal-item">
                                        <label>Salary Expected $</label>
                                            <input type="text" name="salary_expected" placeholder="HKD" class="form-control custom-input name-input" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <!--  -->
                    <div class="right-card-wrap">
                        <div class="right-card-content">
                                @if(!empty($questions))
                                    @foreach($questions as $q)
                                        <div class="mb-4">
                                            <label class="form-label" style="font-weight: 500;">{{ $q->question }}</label>
                                            <input type="text" class="form-control custom-input" name="question[{{ $q->id }}]" placeholder="please answer">
                                </div>
                                    @endforeach
                                @endif
                                        </div>
                                        </div>
                                    </div>




                <!-- Qualifications -->
                <div class="right-card-wrap right-card-public" id="qualifications">
                    <div class="content-title d-flex align-items-center">
                        <p>Qualifications</p>
                    </div>
                    <div class="right-card-content">
                        <h4 class="custom-h4">ACADEMIC ATTAINMENT (in chronological order)</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th class="table-th">Issuing Authority</th>
                                    <th class="table-th">Qualifications</th>
                                    <th class="table-th">Issue Date</th>
                                    <th class="table-th">Major Subjects & Level Attained</th>
                                </tr>
                                </thead>
                                <tbody id="table-tbody1">
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[0][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[0][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="academic_attainment[0][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[0][major_subjects]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[1][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[1][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="academic_attainment[1][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[1][major_subjects]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[2][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[2][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="academic_attainment[2][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[2][major_subjects]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[3][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[3][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="academic_attainment[3][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[3][major_subjects]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[4][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[4][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                                <input type="text" name="academic_attainment[4][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="academic_attainment[4][major_subjects]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <button class="btn add-row-btn d-flex justify-content-center align-items-center" id="add-new-row1"
                                    onclick="addRowAction('table-tbody1', 1)">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add a new row</span>
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                <tr class="d-flex align-items-center">
                                    <td class="col-3 min-width-200">
                                        <span>HKEAA</span>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="d-flex justify-content-start">
                                            <div class="col-6">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault8" id="radio1">
                                                <label class="form-check-label ms-2" for="radio1">HKCEE</label>
                                            </div>
                                            <div class="col-6">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault8" id="radio2">
                                                <label class="form-check-label ms-2" for="radio2">HKDSE</label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                            <input type="text" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="d-flex justify-content-around">
                                            <div class="p-1">
                                                <input type="text" placeholder="ENG" class="form-control custom-input name-input" />
                                            </div>
                                            <div class="p-1">
                                                <input type="text" placeholder="CHIN" class="form-control custom-input name-input" />
                                            </div>
                                            <div class="p-1">
                                                <input type="text" placeholder="MATH" class="form-control custom-input name-input" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="d-flex align-items-center">
                                    <td class="col-3 min-width-200">
                                        <span>HKEAA</span>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="d-flex justify-content-start">
                                            <div class="col-6">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault9" id="radio3">
                                                <label class="form-check-label ms-2" for="radio3">HKALE</label>
                                            </div>
                                            <div class="col-6">
                                                <input class="form-check-input" type="radio" name="flexRadioDefault9" id="radio4">
                                                <label class="form-check-label ms-2" for="radio4">N.A.</label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="input-group date custom-input name-input d-flex align-items-center datepicker">
                                            <input type="text" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="d-flex justify-content-around">
                                            <div class="p-1">
                                                <input type="text" placeholder="ENG" class="form-control custom-input name-input" />
                                            </div>
                                            <div class="p-1">
                                                <input type="text" placeholder="CHIN" class="form-control custom-input name-input" />
                                            </div>
                                            <div class="p-1">
                                                <input type="text" placeholder="MATH" class="form-control custom-input name-input" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="d-flex align-items-center">
                                    <td class="col-3 min-width-200">
                                        <span>HKEAA LPAT (ENG)</span>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="col-row">
                                            <input class="form-check-input col-2" type="radio" name="flexRadioDefault10" id="radio5">
                                            <label class="form-check-label ms-2 col-10" for="radio5">Fully Attained/Exemption</label>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="col-row">
                                            <input class="form-check-input col-2" type="radio" name="flexRadioDefault10" id="radio6">
                                            <label class="form-check-label ms-2 col-10" for="radio6">Attained in all 4 core language
                                                skill</label>
                                        </div>
                                    </td>
                                    <td class="col-3 min-width-200">
                                        <div class="col-row">
                                            <input class="form-check-input col-2" type="radio" name="flexRadioDefault10" id="radio7">
                                            <label class="form-check-label ms-2 col-10" for="radio7">Not yet attained</label>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="custom-line"></div>

                        <h4 class="custom-h4">PROFESSIONAL QUALIFICATIONS (e.g. PGDE, Associateship, Certificates, etc.)</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th class="table-th col-3">Full Name of Issuing Authority</th>
                                    <th class="table-th col-3">Professional Qualifications</th>
                                    <th class="table-th col-1">Status</th>
                                    <th class="table-th col-3">Level Attained/To Be Attained</th>
                                    <th class="table-th col-2">Issue Date</th>
                                </tr>
                                </thead>
                                <tbody id="table-tbody2">
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[0][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[0][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="professional_qualification[0][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[0][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="professional_qualification[0][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[1][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[1][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="professional_qualification[1][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[1][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="professional_qualification[1][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[2][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[2][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="professional_qualification[2][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[2][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="professional_qualification[2][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[3][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[3][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="professional_qualification[3][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[3][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="professional_qualification[3][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[4][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[4][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="professional_qualification[4][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="professional_qualification[4][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="professional_qualification[4][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <button class="btn add-row-btn d-flex justify-content-center align-items-center"
                                    onclick="addRowAction('table-tbody2', 2)">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add a new row</span>
                            </button>
                        </div>

                        <div class="custom-line"></div>
                        <h4 class="custom-h4">Special Education Training (Details of training related to supporting students
                            with special educational needs)</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th class="table-th col-3">Full Name of Issuing Authority</th>
                                    <th class="table-th col-3">Professional Qualifications</th>
                                    <th class="table-th col-1">Status</th>
                                    <th class="table-th col-3">Level Attained/To Be Attained</th>
                                    <th class="table-th col-2">Issue Date</th>
                                </tr>
                                </thead>
                                <tbody id="table-tbody3">
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="special_education[0][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[0][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="special_education[0][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[0][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="special_education[0][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="special_education[1][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[1][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="special_education[1][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[1][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="special_education[1][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="special_education[2][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[2][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="special_education[2][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[2][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="special_education[2][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="special_education[3][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[3][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="special_education[3][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[3][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="special_education[3][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                            <input type="text" name="special_education[4][issuing_authority]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[4][qualification]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                            <select name="special_education[4][status]" class="form-select custom-input name-input min-width-150"
                                                aria-label="Default select example">
                                            <option value="0">Completed</option>
                                            <option value="1">Be Complete</option>
                                        </select>
                                    </td>
                                    <td class="table-td">
                                            <input type="text" name="special_education[4][level]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                                <input type="text" name="special_education[4][issue_date]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <button class="btn add-row-btn d-flex justify-content-center align-items-center"
                                    onclick="addRowAction('table-tbody3', 3)">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add a new row</span>
                            </button>
                        </div>



                        </div>
                    </div>
                </div>



                <!-- Work Experience -->
                <div class="right-card-wrap right-card-public" id="work">
                    <div class="content-title d-flex align-items-center">
                        <p>Work Experience</p>
                    </div>
                    <div class="right-card-content">
                        <h4 class="custom-h4">TEACHING / WORKING EXPERIENCE</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th class="table-th">Employer</th>
                                    <th class="table-th">Position Held</th>
                                    <th class="table-th">Date From</th>
                                    <th class="table-th">To</th>
                                </tr>
                                </thead>
                                <tbody id="table-tbody4">
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[0][employer]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[0][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[0][date_from]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[0][date_to]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[1][employer]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[1][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[1][date_from]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[1][date_to]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[2][employer]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[2][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[2][date_from]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[2][date_to]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[3][employer]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[3][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[3][date_from]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[3][date_to]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[4][employer]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="work_experience[4][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[4][date_from]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                    <td class="table-td">
                                        <div
                                            class="input-group date custom-input name-input d-flex align-items-center datepicker min-width-150">
                                            <input type="text" name="work_experience[4][date_to]" class="form-control opacity-input">
                                            <span class="input-group-addon">
															<i class="fas fa-calendar-alt m-2" style="color: #0087A4;"></i>
														</span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <button class="btn add-row-btn d-flex justify-content-center align-items-center"
                                    onclick="addRowAction('table-tbody4', 4)">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add a new row</span>
                            </button>
                        </div>

                        <h4 class="custom-h4">REFEREE INFORMATION</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th class="table-th col-2">Name</th>
                                    <th class="table-th col-6">Company</th>
                                    <th class="table-th col-2">Position</th>
                                    <th class="table-th col-2">Contact Number</th>
                                </tr>
                                </thead>
                                <tbody id="table-tbody5">
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="referee[0][name]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[0][company]"
                                               class="form-control custom-input name-input min-width-200" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[0][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[0][contact_number]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="table-td">
                                        <input type="text" name="referee[1][name]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[1][company]"
                                               class="form-control custom-input name-input min-width-200" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[1][position]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                    <td class="table-td">
                                        <input type="text" name="referee[1][contact_number]"
                                               class="form-control custom-input name-input min-width-150" />
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <button class="btn add-row-btn d-flex justify-content-center align-items-center"
                                    onclick="addRowAction('table-tbody5', 5)">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add a new row</span>
                            </button>
                        </div>


                        <div class="custom-line"></div>
                        <h4 class="custom-h4">PDF UPPLOAD</h4>
                        <p>Please combine all documents, such as a compelling applicatoin letter, a detailed CV, a positive
                            reference letter, and vaild salary proof, into one PDF file for a complete submission. The maximum
                            file size is 50MB</p>
                        <!-- <div class="d-flex align-items-center pb-3">
                            <i class="fas fa-file-upload" style="color: #0087A4; font-size: 1.6rem;"></i>
                            <span class="ms-2" style="font: normal normal 600 1.25rem/1.125rem Raleway;">XXXXXXXX.pdf</span>
                        </div> -->

                        <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
                        <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />

                        <div class="upload-file">
                            <div class="choose-file d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-file-upload" style="color: #0087A4; font-size: 2.5rem;"></i>
                                <h6>Select a PDF file to upload</h6>
                                <p>or drag and drop it here</p>
                            </div>
                            <div id="my-awesome-dropzone" class="dropzone"></div>
                            <input type="hidden" name="resume_file" id="resume_file_input">
                        </div>

                        <div id="uploaded-files" class="mt-3">
                            <!-- 上传文件列表将显示在这里 -->
                        </div>

                        <script>
                            Dropzone.autoDiscover = false; // 禁用自动发现
                            var myDropzone = new Dropzone("#my-awesome-dropzone", {
                                url: "{{ route('job.upload.resume') }}", // 设置文件上传的路由
                                acceptedFiles: ".pdf",
                                maxFilesize: 50, // 最大文件大小（MB）
                                maxFiles: 1, // 最多上传1个文件
                                addRemoveLinks: true, // 添加删除链接
                                dictDefaultMessage: "拖放文件到此处或点击上传",
                                dictFileTooBig: "文件太大，最大允许50MB",
                                dictInvalidFileType: "只能上传PDF文件",
                                dictMaxFilesExceeded: "最多只能上传1个文件",
                                dictRemoveFile: "删除文件",
                                headers: {
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                init: function() {
                                    this.on("success", function(file, response) {
                                        // 上传成功后，将文件路径保存到隐藏输入框
                                        document.getElementById('resume_file_input').value = response.filepath;
                                        
                                        // 显示上传成功消息
                                        let successDiv = document.createElement('div');
                                        successDiv.className = 'alert alert-success';
                                        successDiv.innerHTML = '文件 <strong>' + file.name + '</strong> 上传成功';
                                        document.getElementById('uploaded-files').appendChild(successDiv);
                                    });
                                    
                                    this.on("error", function(file, errorMessage, xhr) {
                                        // 处理错误消息，确保正确显示
                                        let message = '';

                                        if (typeof errorMessage === 'object') {
                                            // 如果是对象，尝试获取message属性
                                            message = errorMessage.message || '上传失败，请重试';
                                        } else if (typeof errorMessage === 'string') {
                                            message = errorMessage;
                                        } else {
                                            message = '上传失败，请重试';
                                        }

                                        // 显示错误消息
                                        let errorDiv = document.createElement('div');
                                        errorDiv.className = 'alert alert-danger alert-dismissible fade show';
                                        errorDiv.innerHTML = `
                                            <strong>上传失败:</strong> ${message}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                                        `;
                                        document.getElementById('uploaded-files').appendChild(errorDiv);

                                        // 移除失败的文件，允许重新上传
                                        this.removeFile(file);
                                    });
                                    
                                    this.on("removedfile", function(file) {
                                        // 当文件被删除时，清除隐藏输入框的值
                                        document.getElementById('resume_file_input').value = '';
                                        document.getElementById('uploaded-files').innerHTML = '';
                                    });
                                }
                            });
                        </script>
                    </div>
                </div>



                <!-- 提交按钮 -->
                <div class="d-flex justify-content-center align-items-center mt-4" style="margin-bottom: 12.5rem;">
                    <button type="submit" class="btn next-btn custom-btn-color1">
                        PREVIEW
                        <i class="fas fa-angle-right" style="float: right; margin-right: 1.25rem;"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- <div class="d-flex flex-row justify-content-start">


    </div> -->

</div>

<script src="{{asset('static/bootstrap/js/bootstrap.bundle.min.js')}}"></script>
<script src="{{asset('static/js/jquery-3.7.1.min.js')}}"></script>
<script src="{{asset('static/bootstrap/js/bootstrap-datepicker.min.js')}}"></script>
<script src="{{asset('static/js/information.js')}}"></script>
</body>
</html>
