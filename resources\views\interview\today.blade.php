@extends('layouts.app')
@section('page-title')
    {{ __('Today Interview') }}
@endsection
@section('breadcrumb')
    <ul class="breadcrumb mb-0">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
        </li>
        <li class="breadcrumb-item active">
            <a href="#">{{ __('Today Interview') }}</a>
        </li>
    </ul>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5>{{ __('Today Interview List') }}</h5>
                        </div>

                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="dt-responsive table-responsive">
                        <table class="table table-hover advance-datatable">
                            <thead>
                                <tr>
                                    <th>{{ __('Applicant') }}</th>
                                    <th>{{ __('Phone Number') }}</th>
                                    <th>{{ __('Interview Time') }}</th>
                                    <th>{{ __('Interview Duration') }}</th>
                                    <th>{{ __('Assign User') }}</th>
                                    <th>{{ __('Resume') }}</th>
                                    @if (Gate::check('edit interview') || Gate::check('delete interview') || Gate::check('show interview'))
                                        <th>{{ __('Action') }}</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($interviews as $interview)
                                    <tr>

                                        <td>
                                            <img src="{{ (!empty($interview->applicants) && !empty($interview->applicants->profile)) || (!empty($interview->applicant_name) && !empty($interview->profile)) ? asset('/storage/upload/applicant/profile/' . (isset($interview->applicants->profile) ? $interview->applicants->profile : $interview->profile)) : asset('/storage/upload/profile/avatar.png') }}"
                                                alt="contact-img" title="contact-img" class="rounded mr-3" height="48" />
                                            <p class="m-0 d-inline-block align-middle font-16">
                                                <a href="#" class="text-body">
                                                    {{ !empty($interview->applicants) ? $interview->applicants->name : (!empty($interview->applicant_name) ? $interview->applicant_name : '') }}
                                                </a>
                                                <br />
                                                {{ !empty($interview->applicants) ? $interview->applicants->email : (!empty($interview->applicant_email) ? $interview->applicant_email : '-') }}
                                            </p>
                                        </td>
                                        <td>
                                            {{ !empty($interview->applicants) ? $interview->applicants->phone : (!empty($interview->applicant_phone) ? $interview->applicant_phone : '-') }}
                                        </td>
                                        <td>
                                            {{ timeFormat($interview->interview_start_time) }} -
                                            {{ timeFormat($interview->interview_end_time) }}
                                        </td>
                                        <td>
                                            {{ $interview->total_duration }}
                                        </td>
                                        <td>
                                            {{ !empty($interview->users) ? $interview->users->name : (!empty($interview->user_name) ? $interview->user_name : '-') }}
                                        </td>
                                        <td>
                                            @if ((!empty($interview->applicants) && !empty($interview->applicants->resume)) || (!empty($interview->applicant_resume)))
                                                <a href="{{ asset(Storage::url('upload/applicant/resume')) . '/' . (!empty($interview->applicants) ? $interview->applicants->resume : $interview->applicant_resume) }}"
                                                    target="_blank"><i data-feather="download"></i></a>
                                            @else
                                                -
                                            @endif
                                        </td>
                                        @if (Gate::check('edit interview') || Gate::check('delete interview') || Gate::check('show interview'))
                                            <td class="text-right">
                                                <div class="cart-action">
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['interview.destroy', $interview->id]]) !!}
                                                    @if (Gate::check('show interview'))
                                                        <a class="avtar avtar-xs btn-link-warning text-warning customModal"
                                                            data-bs-toggle="tooltip"
                                                            data-bs-original-title="{{ __('Detail') }}" data-size="md"
                                                            href="#"
                                                            data-url="{{ route('interview.show', $interview->id) }}"
                                                            data-title="{{ __('Interview Detail') }}"> <i
                                                                data-feather="eye"></i></a>
                                                    @endif

                                                    @if (Gate::check('edit interview'))
                                                        <a class="avtar avtar-xs btn-link-secondary text-secondary customModal"
                                                            data-bs-toggle="tooltip"
                                                            data-bs-original-title="{{ __('Edit') }}" data-size="md"
                                                            href="#"
                                                            data-url="{{ route('interview.edit', $interview->id) }}"
                                                            data-title="{{ __('Edit Interview') }}"> <i
                                                                data-feather="edit"></i></a>
                                                    @endif
                                                    @if (Gate::check('delete interview'))
                                                        <a class="avtar avtar-xs btn-link-danger text-danger confirm_dialog"
                                                            data-bs-toggle="tooltip"
                                                            data-bs-original-title="{{ __('Detete') }}" href="#"> <i
                                                                data-feather="trash-2"></i></a>
                                                    @endif
                                                    {!! Form::close() !!}
                                                </div>
                                            </td>
                                        @endif
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
